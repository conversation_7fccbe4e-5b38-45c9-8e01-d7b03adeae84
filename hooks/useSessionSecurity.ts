/**
 * Session Security Hook
 *
 * React hook for monitoring session security, handling idle timeouts,
 * and providing real-time session status updates.
 */

'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import {
  validateSessionSecurity,
  trackSessionActivity,
  generateDeviceId,
  refreshSessionIfNeeded,
  cleanupSessionData,
  isSessionIdle,
  resetSessionSecurityFlags,
  SESSION_CONFIG,
  SessionSecurityInfo
} from '@/utils/enhanced-session-manager';

export interface UseSessionSecurityOptions {
  // Enable automatic session monitoring
  enableMonitoring?: boolean;
  // Enable idle timeout detection
  enableIdleTimeout?: boolean;
  // Enable automatic session refresh
  enableAutoRefresh?: boolean;
  // Custom idle timeout (milliseconds)
  idleTimeout?: number;
  // Callback when session becomes invalid
  onSessionInvalid?: (reason: string) => void;
  // Callback when security issues are detected
  onSecurityIssue?: (flags: SessionSecurityInfo['securityFlags']) => void;
}

export interface SessionSecurityState {
  isLoading: boolean;
  sessionInfo: SessionSecurityInfo | null;
  isAuthenticated: boolean;
  timeUntilExpiry: number | null;
  timeUntilIdle: number | null;
  securityWarnings: string[];
}

export function useSessionSecurity(options: UseSessionSecurityOptions = {}) {
  const {
    enableMonitoring = true,
    enableIdleTimeout = true,
    enableAutoRefresh = true,
    idleTimeout = SESSION_CONFIG.MAX_IDLE_TIME,
    onSessionInvalid,
    onSecurityIssue
  } = options;

  const router = useRouter();
  const [state, setState] = useState<SessionSecurityState>({
    isLoading: true,
    sessionInfo: null,
    isAuthenticated: false,
    timeUntilExpiry: null,
    timeUntilIdle: null,
    securityWarnings: []
  });

  const monitoringIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const idleCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const activityTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Validate session and update state
   */
  const validateSession = useCallback(async () => {
    try {
      const sessionInfo = await validateSessionSecurity();

      const securityWarnings: string[] = [];

      // Check for security issues
      if (sessionInfo.securityFlags.suspiciousActivity) {
        securityWarnings.push('Suspicious activity detected');
      }
      if (sessionInfo.securityFlags.multipleDevices) {
        securityWarnings.push('Multiple devices detected');
      }
      if (sessionInfo.securityFlags.locationChange) {
        securityWarnings.push('Location change detected');
      }

      // Calculate time until expiry
      let timeUntilExpiry: number | null = null;
      if (sessionInfo.expiresAt) {
        timeUntilExpiry = Math.max(0, sessionInfo.expiresAt - Date.now() / 1000);
      }

      // Calculate time until idle
      let timeUntilIdle: number | null = null;
      if (sessionInfo.lastActivity) {
        const timeSinceActivity = Date.now() - sessionInfo.lastActivity;
        timeUntilIdle = Math.max(0, idleTimeout - timeSinceActivity);
      }

      setState({
        isLoading: false,
        sessionInfo,
        isAuthenticated: sessionInfo.isValid,
        timeUntilExpiry,
        timeUntilIdle,
        securityWarnings
      });

      // Handle session invalid
      if (!sessionInfo.isValid && onSessionInvalid) {
        onSessionInvalid(sessionInfo.error || 'Session invalid');
      }

      // Handle security issues
      if (securityWarnings.length > 0 && onSecurityIssue) {
        onSecurityIssue(sessionInfo.securityFlags);
      }

      // Redirect if session is invalid
      if (!sessionInfo.isValid) {
        router.push('/sign-in');
      }

    } catch (error) {
      console.error('Error validating session:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        isAuthenticated: false,
        securityWarnings: ['Session validation failed']
      }));
    }
  }, [router, idleTimeout, onSessionInvalid, onSecurityIssue]);

  /**
   * Track user activity
   */
  const trackActivity = useCallback((action: string = 'user_activity') => {
    const deviceId = generateDeviceId();
    trackSessionActivity(action, deviceId);

    // Reset idle timeout
    if (activityTimeoutRef.current) {
      clearTimeout(activityTimeoutRef.current);
    }

    // Set new idle timeout
    if (enableIdleTimeout) {
      activityTimeoutRef.current = setTimeout(() => {
        validateSession();
      }, idleTimeout);
    }
  }, [enableIdleTimeout, idleTimeout, validateSession]);

  /**
   * Handle session refresh
   */
  const handleSessionRefresh = useCallback(async () => {
    if (!enableAutoRefresh) return;

    try {
      const refreshed = await refreshSessionIfNeeded();
      if (refreshed) {
        trackActivity('session_refresh');
        await validateSession();
      }
    } catch (error) {
      console.error('Error refreshing session:', error);
    }
  }, [enableAutoRefresh, trackActivity, validateSession]);

  /**
   * Force session validation
   */
  const forceValidation = useCallback(() => {
    validateSession();
  }, [validateSession]);

  /**
   * Clear security warnings (useful for false positives)
   */
  const clearSecurityWarnings = useCallback(() => {
    resetSessionSecurityFlags();
    setState(prev => ({
      ...prev,
      securityWarnings: []
    }));
    // Re-validate after clearing
    setTimeout(() => {
      validateSession();
    }, 500);
  }, [validateSession]);

  /**
   * Sign out and cleanup
   */
  const signOut = useCallback(async () => {
    try {
      const { createClient } = await import('@/utils/supabase/client');
      const supabase = createClient();
      await supabase.auth.signOut();

      // Clean up session data
      cleanupSessionData();

      // Clear intervals
      if (monitoringIntervalRef.current) {
        clearInterval(monitoringIntervalRef.current);
      }
      if (idleCheckIntervalRef.current) {
        clearInterval(idleCheckIntervalRef.current);
      }
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      if (activityTimeoutRef.current) {
        clearTimeout(activityTimeoutRef.current);
      }

      router.push('/sign-in');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }, [router]);

  // Set up monitoring intervals
  useEffect(() => {
    if (!enableMonitoring) return;

    // Reset security flags for new sessions to prevent false positives
    resetSessionSecurityFlags();

    // Initial validation (with a small delay to allow reset to take effect)
    setTimeout(() => {
      validateSession();
    }, 1000);

    // Set up periodic session validation
    monitoringIntervalRef.current = setInterval(() => {
      validateSession();
    }, SESSION_CONFIG.SECURITY_CHECK_INTERVAL);

    // Set up session refresh check
    if (enableAutoRefresh) {
      refreshIntervalRef.current = setInterval(() => {
        handleSessionRefresh();
      }, SESSION_CONFIG.REFRESH_THRESHOLD);
    }

    // Set up idle check
    if (enableIdleTimeout) {
      idleCheckIntervalRef.current = setInterval(() => {
        if (isSessionIdle()) {
          validateSession();
        }
      }, 60000); // Check every minute
    }

    return () => {
      if (monitoringIntervalRef.current) {
        clearInterval(monitoringIntervalRef.current);
      }
      if (idleCheckIntervalRef.current) {
        clearInterval(idleCheckIntervalRef.current);
      }
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      if (activityTimeoutRef.current) {
        clearTimeout(activityTimeoutRef.current);
      }
    };
  }, [enableMonitoring, enableAutoRefresh, enableIdleTimeout, validateSession, handleSessionRefresh]);

  // Set up activity tracking
  useEffect(() => {
    if (!enableIdleTimeout) return;

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

    const handleActivity = () => {
      trackActivity('user_interaction');
    };

    // Add event listeners
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Initial activity tracking
    trackActivity('session_start');

    return () => {
      // Remove event listeners
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [enableIdleTimeout, trackActivity]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupSessionData();
    };
  }, []);

  return {
    ...state,
    trackActivity,
    forceValidation,
    clearSecurityWarnings,
    signOut,
    refreshSession: handleSessionRefresh
  };
}
