/**
 * Fast Authentication Hook
 *
 * Provides lightning-fast authentication state management with intelligent
 * caching and minimal loading states. Optimized for user experience.
 */

'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import {
  validateSessionFast,
  addSessionListener,
  getCurrentSessionState,
  forceSessionValidation,
  refreshSessionProactively,
  type SessionState
} from '@/utils/enhanced-session-manager';
import { createClient } from '@/utils/supabase/client';

export interface FastAuthState {
  user: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  sessionState: SessionState | null;
  error: string | null;
  isInitialized: boolean;
}

export interface UseFastAuthOptions {
  redirectTo?: string;
  requireAuth?: boolean;
  enableAutoRefresh?: boolean;
  onAuthChange?: (user: any | null) => void;
  onSessionExpiring?: (timeLeft: number) => void;
}

/**
 * Fast authentication hook with intelligent caching
 */
export function useFastAuth(options: UseFastAuthOptions = {}) {
  const {
    redirectTo = '/sign-in',
    requireAuth = false,
    enableAutoRefresh = true,
    onAuthChange,
    onSessionExpiring
  } = options;

  const router = useRouter();
  const [authState, setAuthState] = useState<FastAuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    sessionState: null,
    error: null,
    isInitialized: false
  });

  const initializationRef = useRef(false);
  const listenerCleanupRef = useRef<(() => void) | null>(null);

  // Update auth state from session state
  const updateAuthFromSession = useCallback((sessionState: SessionState) => {
    const isAuthenticated = sessionState.isValid && !!sessionState.user;

    setAuthState(prev => ({
      ...prev,
      user: sessionState.user,
      isAuthenticated,
      sessionState,
      error: sessionState.error || null,
      isLoading: false,
      isInitialized: true
    }));

    // Trigger auth change callback
    if (onAuthChange) {
      onAuthChange(sessionState.user);
    }

    // Trigger session expiring callback
    if (onSessionExpiring && sessionState.isValid && sessionState.timeUntilExpiry < 900) {
      onSessionExpiring(sessionState.timeUntilExpiry);
    }

    // Handle authentication requirements
    if (requireAuth && !isAuthenticated && typeof window !== 'undefined') {
      if (window.location.pathname !== redirectTo) {
        router.push(redirectTo);
      }
    }
  }, [requireAuth, redirectTo, router, onAuthChange, onSessionExpiring]);

  // Fast initialization using cached data
  const initializeAuth = useCallback(async () => {
    if (initializationRef.current) return;
    initializationRef.current = true;

    try {
      // First, try to get cached session state for instant response
      const cachedState = getCurrentSessionState();
      if (cachedState) {
        updateAuthFromSession(cachedState);

        // If cached state is valid, we can show content immediately
        if (cachedState.isValid) {
          return;
        }
      }

      // If no valid cached state, perform fast validation
      const sessionState = await validateSessionFast();
      updateAuthFromSession(sessionState);
    } catch (error) {
      console.error('Auth initialization error:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        isInitialized: true,
        error: error instanceof Error ? error.message : 'Authentication error'
      }));
    }
  }, [updateAuthFromSession]);

  // Set up session listener for real-time updates
  useEffect(() => {
    // Clean up previous listener
    if (listenerCleanupRef.current) {
      listenerCleanupRef.current();
    }

    // Add new listener
    listenerCleanupRef.current = addSessionListener(updateAuthFromSession);

    return () => {
      if (listenerCleanupRef.current) {
        listenerCleanupRef.current();
      }
    };
  }, [updateAuthFromSession]);

  // Initialize authentication on mount
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  // Set up Supabase auth state listener for auth changes
  useEffect(() => {
    const supabase = createClient();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        // console.log('Auth state change detected:', event, !!session);

        // Handle different auth events
        if (event === 'SIGNED_IN' && session) {
          // User just signed in - force fresh validation
          const sessionState = await forceSessionValidation();
          updateAuthFromSession(sessionState);
        } else if (event === 'SIGNED_OUT') {
          // User signed out - clear state immediately
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
            sessionState: null,
            error: null,
            isInitialized: true
          });
        } else if (event === 'TOKEN_REFRESHED' && session) {
          // Token was refreshed - update session state
          const sessionState = await forceSessionValidation();
          updateAuthFromSession(sessionState);
        } else {
          // Other events - force validation
          const sessionState = await forceSessionValidation();
          updateAuthFromSession(sessionState);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [updateAuthFromSession]);

  // Auto-refresh session if enabled
  useEffect(() => {
    if (!enableAutoRefresh || !authState.sessionState?.needsRefresh) {
      return;
    }

    const refreshTimer = setTimeout(() => {
      refreshSessionProactively();
    }, 1000); // Refresh after 1 second delay

    return () => clearTimeout(refreshTimer);
  }, [enableAutoRefresh, authState.sessionState?.needsRefresh]);

  // Manual refresh function
  const refresh = useCallback(async () => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const sessionState = await forceSessionValidation();
      updateAuthFromSession(sessionState);
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Refresh failed'
      }));
    }
  }, [updateAuthFromSession]);

  // Sign out function
  const signOut = useCallback(async () => {
    try {
      const supabase = createClient();
      await supabase.auth.signOut();

      // Update state immediately
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        sessionState: null,
        error: null,
        isInitialized: true
      });

      // Redirect if on protected route
      if (typeof window !== 'undefined' && window.location.pathname.startsWith('/protected')) {
        router.push('/sign-in');
      }
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }, [router]);

  return {
    ...authState,
    refresh,
    signOut
  };
}

/**
 * Hook for components that require authentication
 * Optimized for fast loading and minimal loading states
 */
export function useRequireFastAuth(redirectTo: string = '/sign-in') {
  return useFastAuth({
    requireAuth: true,
    redirectTo,
    enableAutoRefresh: true
  });
}

/**
 * Hook for optional authentication (doesn't redirect)
 * Provides instant auth state with cached data
 */
export function useOptionalFastAuth() {
  return useFastAuth({
    requireAuth: false,
    enableAutoRefresh: false
  });
}

/**
 * Lightweight auth state hook for performance-critical components
 * Uses only cached data, no validation calls
 */
export function useCachedAuthState() {
  const [authState, setAuthState] = useState<{
    user: any | null;
    isAuthenticated: boolean;
    isLoading: boolean;
  }>({
    user: null,
    isAuthenticated: false,
    isLoading: true
  });

  useEffect(() => {
    // Get initial cached state
    const cachedState = getCurrentSessionState();
    if (cachedState) {
      setAuthState({
        user: cachedState.user,
        isAuthenticated: cachedState.isValid && !!cachedState.user,
        isLoading: false
      });
    } else {
      setAuthState(prev => ({ ...prev, isLoading: false }));
    }

    // Listen for session updates
    const cleanup = addSessionListener((sessionState) => {
      setAuthState({
        user: sessionState.user,
        isAuthenticated: sessionState.isValid && !!sessionState.user,
        isLoading: false
      });
    });

    return cleanup;
  }, []);

  return authState;
}

/**
 * Hook for session health monitoring
 */
export function useSessionHealth() {
  const [healthStatus, setHealthStatus] = useState<{
    status: 'healthy' | 'warning' | 'critical' | 'invalid';
    message: string;
    timeUntilExpiry: number;
  }>({
    status: 'invalid',
    message: 'Checking session...',
    timeUntilExpiry: 0
  });

  useEffect(() => {
    const updateHealthStatus = () => {
      const cachedState = getCurrentSessionState();
      if (!cachedState || !cachedState.isValid) {
        setHealthStatus({
          status: 'invalid',
          message: 'Session invalid',
          timeUntilExpiry: 0
        });
        return;
      }

      if (cachedState.isExpired) {
        setHealthStatus({
          status: 'critical',
          message: 'Session expired',
          timeUntilExpiry: 0
        });
        return;
      }

      if (cachedState.timeUntilExpiry < 300) { // Less than 5 minutes
        setHealthStatus({
          status: 'critical',
          message: 'Session expiring soon',
          timeUntilExpiry: cachedState.timeUntilExpiry
        });
        return;
      }

      if (cachedState.timeUntilExpiry < 900) { // Less than 15 minutes
        setHealthStatus({
          status: 'warning',
          message: 'Session will expire soon',
          timeUntilExpiry: cachedState.timeUntilExpiry
        });
        return;
      }

      setHealthStatus({
        status: 'healthy',
        message: 'Session active',
        timeUntilExpiry: cachedState.timeUntilExpiry
      });
    };

    // Initial check
    updateHealthStatus();

    // Listen for session updates
    const cleanup = addSessionListener(() => {
      updateHealthStatus();
    });

    // Update every 30 seconds
    const interval = setInterval(updateHealthStatus, 30000);

    return () => {
      cleanup();
      clearInterval(interval);
    };
  }, []);

  return healthStatus;
}
