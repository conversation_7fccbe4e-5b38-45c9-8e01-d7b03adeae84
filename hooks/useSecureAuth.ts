/**
 * Secure Authentication Hook
 *
 * Provides secure authentication state management that validates
 * with the server and cannot be easily bypassed.
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { validateSessionClient, type SessionInfo } from '@/utils/session-manager';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';

export interface AuthState {
  user: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  sessionInfo: SessionInfo | null;
  error: string | null;
}

export interface UseSecureAuthOptions {
  redirectTo?: string;
  requireAuth?: boolean;
  onAuthChange?: (user: any | null) => void;
}

/**
 * Secure authentication hook that validates with server
 */
export function useSecureAuth(options: UseSecureAuthOptions = {}) {
  const { redirectTo = '/sign-in', requireAuth = false, onAuthChange } = options;
  const router = useRouter();
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    sessionInfo: null,
    error: null,
  });
  const [lastValidation, setLastValidation] = useState<number>(0);

  const validateAuth = useCallback(async (force: boolean = false) => {
    try {
      // Cache validation for 30 seconds to prevent excessive re-validation
      const now = Date.now();
      const cacheTime = 30 * 1000; // 30 seconds

      if (!force && (now - lastValidation) < cacheTime && authState.user) {
        // Use cached result if recent and user exists
        return;
      }

      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      const sessionInfo = await validateSessionClient();
      setLastValidation(now);

      if (sessionInfo.isValid && sessionInfo.user) {
        setAuthState({
          user: sessionInfo.user,
          isLoading: false,
          isAuthenticated: true,
          sessionInfo,
          error: null,
        });

        if (onAuthChange) {
          onAuthChange(sessionInfo.user);
        }
      } else {
        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
          sessionInfo,
          error: sessionInfo.error || 'Authentication failed',
        });

        if (onAuthChange) {
          onAuthChange(null);
        }

        // Only redirect if authentication is required AND we're not already on the redirect page
        if (requireAuth && window.location.pathname !== redirectTo) {
          router.push(redirectTo);
        }
      }
    } catch (error) {
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        sessionInfo: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      if (onAuthChange) {
        onAuthChange(null);
      }

      if (requireAuth && window.location.pathname !== redirectTo) {
        router.push(redirectTo);
      }
    }
  }, [requireAuth, redirectTo, router, onAuthChange, lastValidation, authState.user]);

  // Initial authentication check - only run once on mount
  useEffect(() => {
    validateAuth();
  }, []); // Empty dependency array to run only once

  // Listen for auth state changes
  useEffect(() => {
    const supabase = createClient();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        // Prevent validation loops by handling events more carefully
        if (event === 'SIGNED_OUT') {
          setAuthState({
            user: null,
            isLoading: false,
            isAuthenticated: false,
            sessionInfo: null,
            error: null,
          });

          if (onAuthChange) {
            onAuthChange(null);
          }
        } else if (event === 'SIGNED_IN' && session?.user) {
          // Set auth state directly from session to avoid validation loop
          setAuthState({
            user: session.user,
            isLoading: false,
            isAuthenticated: true,
            sessionInfo: {
              isValid: true,
              isExpired: false,
              expiresAt: session.expires_at || null,
              user: session.user,
            },
            error: null,
          });

          if (onAuthChange) {
            onAuthChange(session.user);
          }
        } else if (event === 'TOKEN_REFRESHED') {
          // Force re-validation on token refresh
          await validateAuth(true);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [validateAuth, onAuthChange]);

  // Refresh authentication state
  const refresh = useCallback(async () => {
    await validateAuth(true); // Force refresh
  }, [validateAuth]);

  // Sign out function
  const signOut = useCallback(async () => {
    try {
      const supabase = createClient();
      await supabase.auth.signOut();

      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        sessionInfo: null,
        error: null,
      });

      if (onAuthChange) {
        onAuthChange(null);
      }

      router.push('/sign-in');
    } catch (error) {
      // Handle sign out error silently
    }
  }, [router, onAuthChange]);

  return {
    ...authState,
    refresh,
    signOut,
    validateAuth,
  };
}

/**
 * Hook for components that require authentication
 */
export function useRequireAuth(redirectTo: string = '/sign-in') {
  return useSecureAuth({
    requireAuth: true,
    redirectTo,
  });
}

/**
 * Hook for optional authentication (doesn't redirect)
 */
export function useOptionalAuth() {
  return useSecureAuth({
    requireAuth: false,
  });
}

/**
 * Hook that provides authentication state without automatic validation
 * Use this for performance-sensitive components that don't need real-time validation
 */
export function useAuthState() {
  const [user, setUser] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const supabase = createClient();

    // Get initial user
    supabase.auth.getUser().then(({ data: { user } }) => {
      setUser(user);
      setIsLoading(false);
    });

    // Listen for changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_, session) => {
        setUser(session?.user ?? null);
        setIsLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
  };
}
