/**
 * Environment Variable Validation Utility
 * 
 * This utility ensures all required environment variables are present
 * and properly formatted before the application starts.
 * 
 * SECURITY NOTE: This helps prevent runtime errors due to missing
 * credentials and ensures proper environment setup.
 */

interface EnvironmentConfig {
  // Supabase Configuration
  NEXT_PUBLIC_SUPABASE_URL: string;
  NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
  SUPABASE_SERVICE_ROLE_KEY: string;
  
  // External API Keys
  OPENAI_API_KEY?: string;
  RESEND_API_KEY?: string;
  GROQ_API_KEY?: string;
  GOOGLE_GENERATIVE_AI_API_KEY?: string;
  GOOGLE_API_KEY?: string;
  
  // LiveKit Configuration
  NEXT_PUBLIC_LIVEKIT_URL?: string;
  LIVEKIT_API_KEY?: string;
  LIVEKIT_API_SECRET?: string;
  
  // S3 Configuration
  SUPABASE_S3_ACCESS_KEY_ID?: string;
  SUPABASE_S3_SECRET_ACCESS_KEY?: string;
}

/**
 * Validates that all required environment variables are present
 * @throws Error if any required variables are missing
 */
export function validateEnvironmentVariables(): EnvironmentConfig {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];

  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}\n` +
      'Please check your .env.local file and ensure all required variables are set.'
    );
  }

  // Validate URL format
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  try {
    new URL(supabaseUrl);
  } catch {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL must be a valid URL');
  }

  // Validate Supabase URL format
  if (!supabaseUrl.includes('.supabase.co')) {
    console.warn('Warning: NEXT_PUBLIC_SUPABASE_URL does not appear to be a Supabase URL');
  }

  // Validate JWT format for keys (basic check)
  const jwtPattern = /^eyJ[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
  
  if (!jwtPattern.test(process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!)) {
    throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY does not appear to be a valid JWT');
  }
  
  if (!jwtPattern.test(process.env.SUPABASE_SERVICE_ROLE_KEY!)) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY does not appear to be a valid JWT');
  }

  return {
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY!,
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    RESEND_API_KEY: process.env.RESEND_API_KEY,
    GROQ_API_KEY: process.env.GROQ_API_KEY,
    GOOGLE_GENERATIVE_AI_API_KEY: process.env.GOOGLE_GENERATIVE_AI_API_KEY,
    GOOGLE_API_KEY: process.env.GOOGLE_API_KEY,
    NEXT_PUBLIC_LIVEKIT_URL: process.env.NEXT_PUBLIC_LIVEKIT_URL,
    LIVEKIT_API_KEY: process.env.LIVEKIT_API_KEY,
    LIVEKIT_API_SECRET: process.env.LIVEKIT_API_SECRET,
    SUPABASE_S3_ACCESS_KEY_ID: process.env.SUPABASE_S3_ACCESS_KEY_ID,
    SUPABASE_S3_SECRET_ACCESS_KEY: process.env.SUPABASE_S3_SECRET_ACCESS_KEY,
  };
}

/**
 * Validates environment variables for specific features
 */
export function validateFeatureEnvironment(feature: 'openai' | 'resend' | 'livekit' | 's3'): void {
  switch (feature) {
    case 'openai':
      if (!process.env.OPENAI_API_KEY) {
        throw new Error('OPENAI_API_KEY is required for AI features');
      }
      if (!process.env.OPENAI_API_KEY.startsWith('sk-')) {
        throw new Error('OPENAI_API_KEY must start with "sk-"');
      }
      break;
      
    case 'resend':
      if (!process.env.RESEND_API_KEY) {
        throw new Error('RESEND_API_KEY is required for email features');
      }
      if (!process.env.RESEND_API_KEY.startsWith('re_')) {
        throw new Error('RESEND_API_KEY must start with "re_"');
      }
      break;
      
    case 'livekit':
      const liveKitVars = ['NEXT_PUBLIC_LIVEKIT_URL', 'LIVEKIT_API_KEY', 'LIVEKIT_API_SECRET'];
      const missingLiveKit = liveKitVars.filter(key => !process.env[key]);
      if (missingLiveKit.length > 0) {
        throw new Error(`Missing LiveKit environment variables: ${missingLiveKit.join(', ')}`);
      }
      break;
      
    case 's3':
      const s3Vars = ['SUPABASE_S3_ACCESS_KEY_ID', 'SUPABASE_S3_SECRET_ACCESS_KEY'];
      const missingS3 = s3Vars.filter(key => !process.env[key]);
      if (missingS3.length > 0) {
        throw new Error(`Missing S3 environment variables: ${missingS3.join(', ')}`);
      }
      break;
  }
}

/**
 * Gets the Supabase domain from the URL for use in configurations
 */
export function getSupabaseDomain(): string {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!url) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL not configured');
  }

  try {
    return new URL(url).hostname;
  } catch {
    throw new Error('Invalid NEXT_PUBLIC_SUPABASE_URL format');
  }
}

/**
 * Logs environment validation status (without exposing sensitive data)
 */
export function logEnvironmentStatus(): void {
  const env = process.env.NODE_ENV || 'development';
  console.log(`🔧 Environment: ${env}`);
  
  const hasSupabase = !!(process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
  const hasServiceRole = !!process.env.SUPABASE_SERVICE_ROLE_KEY;
  const hasOpenAI = !!process.env.OPENAI_API_KEY;
  const hasResend = !!process.env.RESEND_API_KEY;
  const hasLiveKit = !!process.env.NEXT_PUBLIC_LIVEKIT_URL;
  
  console.log('🔑 Environment Variables Status:');
  console.log(`  ✅ Supabase: ${hasSupabase ? 'Configured' : '❌ Missing'}`);
  console.log(`  ✅ Service Role: ${hasServiceRole ? 'Configured' : '❌ Missing'}`);
  console.log(`  🤖 OpenAI: ${hasOpenAI ? 'Configured' : '⚠️  Optional'}`);
  console.log(`  📧 Resend: ${hasResend ? 'Configured' : '⚠️  Optional'}`);
  console.log(`  🎥 LiveKit: ${hasLiveKit ? 'Configured' : '⚠️  Optional'}`);
}
