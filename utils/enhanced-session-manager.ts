/**
 * Enhanced Session Management
 *
 * Provides comprehensive session security including activity tracking,
 * idle timeout, cross-device management, and security monitoring.
 */

import { createClient } from '@/utils/supabase/client';
import { createClient as createServerClient } from '@/utils/supabase/server';

export interface SessionSecurityInfo {
  isValid: boolean;
  isExpired: boolean;
  isIdle: boolean;
  expiresAt: number | null;
  lastActivity: number | null;
  user: any | null;
  deviceId: string;
  sessionId: string;
  error?: string;
  securityFlags: {
    suspiciousActivity: boolean;
    multipleDevices: boolean;
    locationChange: boolean;
  };
}

export interface SessionActivity {
  timestamp: number;
  action: string;
  deviceId: string;
  userAgent: string;
  ipAddress?: string;
  location?: string;
}

/**
 * Session configuration
 */
export const SESSION_CONFIG = {
  // Maximum idle time before session is considered inactive (60 minutes - increased further)
  MAX_IDLE_TIME: 60 * 60 * 1000,
  // Activity tracking interval (15 minutes - increased to reduce noise)
  ACTIVITY_INTERVAL: 15 * 60 * 1000,
  // Maximum number of concurrent sessions per user (increased)
  MAX_CONCURRENT_SESSIONS: 5,
  // Session refresh threshold (refresh when 15 minutes left)
  REFRESH_THRESHOLD: 15 * 60 * 1000,
  // Security monitoring interval (5 minutes - increased to reduce performance impact)
  SECURITY_CHECK_INTERVAL: 5 * 60 * 1000
};

/**
 * Generate a unique device ID for session tracking
 */
export function generateDeviceId(): string {
  // Use browser fingerprinting for device identification
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx!.textBaseline = 'top';
  ctx!.font = '14px Arial';
  ctx!.fillText('Device fingerprint', 2, 2);

  const fingerprint = canvas.toDataURL();
  const userAgent = navigator.userAgent;
  const screen = `${window.screen.width}x${window.screen.height}`;
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  const deviceString = `${fingerprint}-${userAgent}-${screen}-${timezone}`;

  // Create a hash of the device string
  let hash = 0;
  for (let i = 0; i < deviceString.length; i++) {
    const char = deviceString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  return `device_${Math.abs(hash).toString(36)}`;
}

/**
 * Track session activity
 */
export function trackSessionActivity(action: string, deviceId: string): void {
  const activity: SessionActivity = {
    timestamp: Date.now(),
    action,
    deviceId,
    userAgent: navigator.userAgent
  };

  // Store in localStorage for persistence
  const activities = getStoredActivities();
  activities.push(activity);

  // Keep only last 50 activities
  const recentActivities = activities.slice(-50);
  localStorage.setItem('session_activities', JSON.stringify(recentActivities));

  // Update last activity timestamp
  localStorage.setItem('last_activity', activity.timestamp.toString());
}

/**
 * Get stored session activities
 */
export function getStoredActivities(): SessionActivity[] {
  try {
    const stored = localStorage.getItem('session_activities');
    return stored ? JSON.parse(stored) : [];
  } catch {
    return [];
  }
}

/**
 * Check if session is idle
 */
export function isSessionIdle(): boolean {
  const lastActivity = localStorage.getItem('last_activity');
  if (!lastActivity) return true;

  const lastActivityTime = parseInt(lastActivity);
  const now = Date.now();

  return (now - lastActivityTime) > SESSION_CONFIG.MAX_IDLE_TIME;
}

/**
 * Enhanced session validation with security checks
 */
export async function validateSessionSecurity(): Promise<SessionSecurityInfo> {
  try {
    const supabase = createClient();
    const deviceId = generateDeviceId();

    // Get user and session
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        isValid: false,
        isExpired: false,
        isIdle: false,
        expiresAt: null,
        lastActivity: null,
        user: null,
        deviceId,
        sessionId: '',
        error: userError?.message || 'No authenticated user',
        securityFlags: {
          suspiciousActivity: false,
          multipleDevices: false,
          locationChange: false
        }
      };
    }

    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      return {
        isValid: false,
        isExpired: false,
        isIdle: false,
        expiresAt: null,
        lastActivity: null,
        user,
        deviceId,
        sessionId: '',
        error: sessionError?.message || 'No valid session',
        securityFlags: {
          suspiciousActivity: false,
          multipleDevices: false,
          locationChange: false
        }
      };
    }

    // Check session expiration
    const now = Date.now() / 1000;
    const isExpired = session.expires_at ? session.expires_at < now : false;

    // Check idle status
    const isIdle = isSessionIdle();

    // Get last activity
    const lastActivityStr = localStorage.getItem('last_activity');
    const lastActivity = lastActivityStr ? parseInt(lastActivityStr) : null;

    // Perform security checks
    const securityFlags = await performSecurityChecks(user.id, deviceId);

    // If session is expired or idle, sign out
    if (isExpired || isIdle) {
      try {
        await supabase.auth.signOut();
      } catch (signOutError) {
        console.error('Error signing out expired/idle session:', signOutError);
      }

      return {
        isValid: false,
        isExpired,
        isIdle,
        expiresAt: session.expires_at || null,
        lastActivity,
        user,
        deviceId,
        sessionId: session.access_token.substring(0, 10),
        error: isExpired ? 'Session expired' : 'Session idle timeout',
        securityFlags
      };
    }

    // Track this validation as activity
    trackSessionActivity('session_validation', deviceId);

    return {
      isValid: true,
      isExpired: false,
      isIdle: false,
      expiresAt: session.expires_at || null,
      lastActivity,
      user,
      deviceId,
      sessionId: session.access_token.substring(0, 10),
      securityFlags
    };

  } catch (error) {
    console.error('Error validating session security:', error);
    return {
      isValid: false,
      isExpired: false,
      isIdle: false,
      expiresAt: null,
      lastActivity: null,
      user: null,
      deviceId: generateDeviceId(),
      sessionId: '',
      error: error instanceof Error ? error.message : 'Unknown error',
      securityFlags: {
        suspiciousActivity: false,
        multipleDevices: false,
        locationChange: false
      }
    };
  }
}

/**
 * Perform security checks on the session
 */
async function performSecurityChecks(userId: string, deviceId: string): Promise<{
  suspiciousActivity: boolean;
  multipleDevices: boolean;
  locationChange: boolean;
}> {
  const activities = getStoredActivities();
  const recentActivities = activities.filter(
    activity => Date.now() - activity.timestamp < 24 * 60 * 60 * 1000 // Last 24 hours
  );

  // Check if this is a new session (less than 10 minutes of activity)
  const sessionStartTime = recentActivities.length > 0 ? recentActivities[0].timestamp : Date.now();
  const sessionAge = Date.now() - sessionStartTime;
  const isNewSession = sessionAge < 10 * 60 * 1000; // Less than 10 minutes

  // Don't flag new sessions as suspicious to prevent false positives during login
  const suspiciousActivity = isNewSession ? false : checkSuspiciousActivity(recentActivities);

  // Check for multiple devices (simplified - in production, use server-side tracking)
  const uniqueDevices = new Set(recentActivities.map(a => a.deviceId));
  const multipleDevices = uniqueDevices.size > 2; // Increased threshold

  // Location change detection would require IP geolocation (simplified here)
  const locationChange = false;

  return {
    suspiciousActivity,
    multipleDevices,
    locationChange
  };
}

/**
 * Check for suspicious activity patterns
 */
function checkSuspiciousActivity(activities: SessionActivity[]): boolean {
  // Need at least 20 activities to make a reasonable assessment
  if (activities.length < 20) return false;

  // Only check activities from the last hour to avoid false positives
  const oneHourAgo = Date.now() - (60 * 60 * 1000);
  const recentActivities = activities.filter(activity => activity.timestamp > oneHourAgo);

  // Need at least 15 recent activities to flag as suspicious
  if (recentActivities.length < 15) return false;

  // Check for rapid successive actions (potential bot activity)
  const rapidActions = recentActivities.filter((activity, index) => {
    if (index === 0) return false;
    const timeDiff = activity.timestamp - recentActivities[index - 1].timestamp;
    return timeDiff < 500; // Less than 0.5 seconds between actions (more reasonable)
  });

  // If more than 50% of recent actions are rapid, flag as suspicious
  // This is a much higher threshold to reduce false positives
  return rapidActions.length / recentActivities.length > 0.5;
}

/**
 * Refresh session if needed
 */
export async function refreshSessionIfNeeded(): Promise<boolean> {
  try {
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.expires_at) return false;

    const now = Date.now() / 1000;
    const timeUntilExpiry = session.expires_at - now;

    // Refresh if less than 15 minutes remaining
    if (timeUntilExpiry < SESSION_CONFIG.REFRESH_THRESHOLD / 1000) {
      const { data: { session: newSession }, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error('Error refreshing session:', error);
        return false;
      }

      trackSessionActivity('session_refresh', generateDeviceId());
      return !!newSession;
    }

    return true;
  } catch (error) {
    console.error('Error checking session refresh:', error);
    return false;
  }
}

/**
 * Clean up expired session data
 */
export function cleanupSessionData(): void {
  try {
    const activities = getStoredActivities();
    const cutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days ago

    const recentActivities = activities.filter(activity => activity.timestamp > cutoff);
    localStorage.setItem('session_activities', JSON.stringify(recentActivities));

    // Clean up other session-related data if needed
    const lastActivity = localStorage.getItem('last_activity');
    if (lastActivity && parseInt(lastActivity) < cutoff) {
      localStorage.removeItem('last_activity');
    }
  } catch (error) {
    console.error('Error cleaning up session data:', error);
  }
}

/**
 * Reset session security flags for new sessions
 * Call this after successful login to prevent false positives
 */
export function resetSessionSecurityFlags(): void {
  try {
    // Clear any existing activities to start fresh
    localStorage.removeItem('session_activities');
    localStorage.setItem('last_activity', Date.now().toString());

    // Track the session reset
    const deviceId = generateDeviceId();
    trackSessionActivity('session_reset', deviceId);
  } catch (error) {
    console.error('Error resetting session security flags:', error);
  }
}
