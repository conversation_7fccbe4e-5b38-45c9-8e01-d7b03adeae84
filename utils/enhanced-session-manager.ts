/**
 * Enhanced Session Management System
 *
 * Provides fast, secure session management with caching, proactive refresh,
 * and seamless user experience. Addresses THREAT #12: WEAK SESSION MANAGEMENT.
 */

import { createClient } from '@/utils/supabase/client';

export interface SessionState {
  isValid: boolean;
  isExpired: boolean;
  expiresAt: number | null;
  user: any | null;
  error?: string;
  lastValidated: number;
  needsRefresh: boolean;
  timeUntilExpiry: number;
}

export interface SessionConfig {
  cacheTimeout: number; // How long to cache validation results (ms)
  refreshThreshold: number; // When to auto-refresh before expiry (seconds)
  maxRetries: number; // Max retry attempts for failed operations
  enableProactiveRefresh: boolean; // Auto-refresh sessions before expiry
}

// Default configuration optimized for performance and security
const DEFAULT_CONFIG: SessionConfig = {
  cacheTimeout: 0, // Disable caching temporarily to debug sign-in issues
  refreshThreshold: 300, // Refresh 5 minutes before expiry
  maxRetries: 3,
  enableProactiveRefresh: false // Disable proactive refresh temporarily
};

// Global session cache to avoid redundant validation calls
let sessionCache: {
  state: SessionState | null;
  timestamp: number;
  config: SessionConfig;
} = {
  state: null,
  timestamp: 0,
  config: DEFAULT_CONFIG
};

// Session event listeners for real-time updates
const sessionListeners = new Set<(state: SessionState) => void>();

/**
 * Configure session management behavior
 */
export function configureSessionManager(config: Partial<SessionConfig>) {
  sessionCache.config = { ...DEFAULT_CONFIG, ...config };
}

/**
 * Add listener for session state changes
 */
export function addSessionListener(listener: (state: SessionState) => void) {
  sessionListeners.add(listener);
  return () => sessionListeners.delete(listener);
}

/**
 * Notify all listeners of session state changes
 */
function notifySessionListeners(state: SessionState) {
  sessionListeners.forEach(listener => {
    try {
      listener(state);
    } catch (error) {
      console.error('Session listener error:', error);
    }
  });
}

/**
 * Fast session validation with intelligent caching
 * Returns cached result if still valid, otherwise performs fresh validation
 */
export async function validateSessionFast(): Promise<SessionState> {
  const now = Date.now();
  const { state, timestamp, config } = sessionCache;

  // Return cached result if still valid
  if (state && (now - timestamp) < config.cacheTimeout) {
    // Update time until expiry
    const updatedState = {
      ...state,
      timeUntilExpiry: state.expiresAt ? Math.max(0, state.expiresAt - (now / 1000)) : 0
    };
    return updatedState;
  }

  // Perform fresh validation
  const newState = await performSessionValidation();

  // Cache the result
  sessionCache = {
    state: newState,
    timestamp: now,
    config
  };

  // Notify listeners
  notifySessionListeners(newState);

  // Schedule proactive refresh if enabled
  if (config.enableProactiveRefresh && newState.isValid && newState.expiresAt) {
    scheduleProactiveRefresh(newState.expiresAt);
  }

  return newState;
}

/**
 * Perform actual session validation (internal)
 */
async function performSessionValidation(): Promise<SessionState> {
  try {
    const supabase = createClient();
    const now = Date.now() / 1000;

    console.log('🔍 Enhanced session manager: Starting validation...');

    // First try to get the session (faster, uses local storage/cookies)
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    console.log('🔍 Session check:', {
      hasSession: !!session,
      hasUser: !!session?.user,
      sessionError: sessionError?.message,
      expiresAt: session?.expires_at
    });

    // If we have a valid session, use it
    if (session && session.user && !sessionError) {
      const isExpired = session.expires_at ? session.expires_at < now : false;
      const timeUntilExpiry = session.expires_at ? Math.max(0, session.expires_at - now) : 0;
      const needsRefresh = timeUntilExpiry > 0 && timeUntilExpiry < sessionCache.config.refreshThreshold;

      if (isExpired) {
        console.log('❌ Session expired, signing out...');
        try {
          await supabase.auth.signOut();
        } catch (signOutError) {
          console.error('Error signing out expired session:', signOutError);
        }

        return {
          isValid: false,
          isExpired: true,
          expiresAt: session.expires_at || null,
          user: session.user,
          error: 'Session expired',
          lastValidated: Date.now(),
          needsRefresh: false,
          timeUntilExpiry: 0
        };
      }

      console.log('✅ Valid session found');
      return {
        isValid: true,
        isExpired: false,
        expiresAt: session.expires_at || null,
        user: session.user,
        lastValidated: Date.now(),
        needsRefresh,
        timeUntilExpiry
      };
    }

    // Fallback to getUser() for additional validation
    console.log('🔄 No session found, trying getUser()...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.log('❌ No user found:', userError?.message);
      return {
        isValid: false,
        isExpired: false,
        expiresAt: null,
        user: null,
        error: userError?.message || 'No authenticated user',
        lastValidated: Date.now(),
        needsRefresh: false,
        timeUntilExpiry: 0
      };
    }

    // If we have a user but no session, create a basic valid state
    console.log('✅ User found without session, creating basic state');
    return {
      isValid: true,
      isExpired: false,
      expiresAt: null,
      user,
      lastValidated: Date.now(),
      needsRefresh: false,
      timeUntilExpiry: 0
    };
  } catch (error) {
    console.error('Session validation error:', error);
    return {
      isValid: false,
      isExpired: false,
      expiresAt: null,
      user: null,
      error: error instanceof Error ? error.message : 'Unknown error',
      lastValidated: Date.now(),
      needsRefresh: false,
      timeUntilExpiry: 0
    };
  }
}

/**
 * Proactively refresh session before expiry
 */
export async function refreshSessionProactively(): Promise<SessionState> {
  try {
    const supabase = createClient();

    // Attempt to refresh the session
    const { data: { session }, error } = await supabase.auth.refreshSession();

    if (error || !session) {
      // If refresh fails, invalidate cache and return current state
      sessionCache.state = null;
      return await validateSessionFast();
    }

    // Update cache with refreshed session
    const now = Date.now();
    const timeUntilExpiry = session.expires_at ? Math.max(0, session.expires_at - (now / 1000)) : 0;

    const newState: SessionState = {
      isValid: true,
      isExpired: false,
      expiresAt: session.expires_at || null,
      user: session.user,
      lastValidated: now,
      needsRefresh: false,
      timeUntilExpiry
    };

    sessionCache = {
      state: newState,
      timestamp: now,
      config: sessionCache.config
    };

    notifySessionListeners(newState);
    return newState;
  } catch (error) {
    console.error('Proactive refresh error:', error);
    return await validateSessionFast();
  }
}

/**
 * Schedule proactive session refresh
 */
function scheduleProactiveRefresh(expiresAt: number) {
  const now = Date.now() / 1000;
  const timeUntilRefresh = (expiresAt - sessionCache.config.refreshThreshold) - now;

  if (timeUntilRefresh > 0) {
    setTimeout(() => {
      refreshSessionProactively();
    }, timeUntilRefresh * 1000);
  }
}

/**
 * Force session validation (bypasses cache)
 */
export async function forceSessionValidation(): Promise<SessionState> {
  sessionCache.state = null;
  return await validateSessionFast();
}

/**
 * Clear session cache
 */
export function clearSessionCache() {
  sessionCache.state = null;
  sessionCache.timestamp = 0;

  // Notify listeners of cache clear
  notifySessionListeners({
    isValid: false,
    isExpired: false,
    expiresAt: null,
    user: null,
    error: 'Cache cleared',
    lastValidated: Date.now(),
    needsRefresh: false,
    timeUntilExpiry: 0
  });
}

/**
 * Initialize session cache on sign-in
 * Call this after successful authentication to ensure fresh session state
 */
export function initializeSessionOnSignIn() {
  // Clear any existing cache
  clearSessionCache();

  // Force immediate validation to populate cache with new session
  setTimeout(() => {
    validateSessionFast();
  }, 100); // Small delay to ensure auth state is updated
}

/**
 * Get current session state from cache (no validation)
 */
export function getCurrentSessionState(): SessionState | null {
  const now = Date.now();
  const { state, timestamp, config } = sessionCache;

  if (state && (now - timestamp) < config.cacheTimeout) {
    return {
      ...state,
      timeUntilExpiry: state.expiresAt ? Math.max(0, state.expiresAt - (now / 1000)) : 0
    };
  }

  return null;
}

/**
 * Check if session needs attention (expiring soon or invalid)
 */
export function getSessionHealthStatus(): {
  status: 'healthy' | 'warning' | 'critical' | 'invalid';
  message: string;
  timeUntilExpiry: number;
} {
  const state = getCurrentSessionState();

  if (!state || !state.isValid) {
    return {
      status: 'invalid',
      message: 'Session invalid',
      timeUntilExpiry: 0
    };
  }

  if (state.isExpired) {
    return {
      status: 'critical',
      message: 'Session expired',
      timeUntilExpiry: 0
    };
  }

  if (state.timeUntilExpiry < 300) { // Less than 5 minutes
    return {
      status: 'critical',
      message: 'Session expiring soon',
      timeUntilExpiry: state.timeUntilExpiry
    };
  }

  if (state.timeUntilExpiry < 900) { // Less than 15 minutes
    return {
      status: 'warning',
      message: 'Session will expire soon',
      timeUntilExpiry: state.timeUntilExpiry
    };
  }

  return {
    status: 'healthy',
    message: 'Session active',
    timeUntilExpiry: state.timeUntilExpiry
  };
}
