/**
 * Client-Side Session Management
 *
 * Provides session validation and management for client-side components.
 * Uses server validation where possible.
 */

import { createClient } from '@/utils/supabase/client';

export interface SessionInfo {
  isValid: boolean;
  isExpired: boolean;
  expiresAt: number | null;
  user: any | null;
  error?: string;
}

/**
 * Validate session on the client side (for client components)
 * Uses server validation but can be called from client
 */
export async function validateSessionClient(): Promise<SessionInfo> {
  try {
    const supabase = createClient();

    // Use getUser() for authentication validation
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError) {
      return {
        isValid: false,
        isExpired: false,
        expiresAt: null,
        user: null,
        error: userError.message
      };
    }

    if (!user) {
      return {
        isValid: false,
        isExpired: false,
        expiresAt: null,
        user: null,
        error: 'No authenticated user'
      };
    }

    // Get session for expiration check
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      return {
        isValid: false,
        isExpired: false,
        expiresAt: null,
        user,
        error: sessionError.message
      };
    }

    if (!session) {
      return {
        isValid: false,
        isExpired: false,
        expiresAt: null,
        user,
        error: 'No valid session'
      };
    }

    // Check if session is expired
    const now = Date.now() / 1000;
    const isExpired = session.expires_at ? session.expires_at < now : false;

    if (isExpired) {
      // Attempt to sign out the expired session
      try {
        await supabase.auth.signOut();
      } catch (signOutError) {
        console.error('Error signing out expired session:', signOutError);
      }

      return {
        isValid: false,
        isExpired: true,
        expiresAt: session.expires_at || null,
        user,
        error: 'Session expired'
      };
    }

    return {
      isValid: true,
      isExpired: false,
      expiresAt: session.expires_at || null,
      user,
    };
  } catch (error) {
    console.error('Error validating client session:', error);
    return {
      isValid: false,
      isExpired: false,
      expiresAt: null,
      user: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Force session refresh
 * Useful when session might be stale
 */
export async function refreshSession(): Promise<SessionInfo> {
  try {
    const supabase = createClient();

    // Force refresh the session
    const { data: { session }, error } = await supabase.auth.refreshSession();

    if (error) {
      return {
        isValid: false,
        isExpired: false,
        expiresAt: null,
        user: null,
        error: error.message
      };
    }

    if (!session) {
      return {
        isValid: false,
        isExpired: false,
        expiresAt: null,
        user: null,
        error: 'Failed to refresh session'
      };
    }

    return {
      isValid: true,
      isExpired: false,
      expiresAt: session.expires_at || null,
      user: session.user,
    };
  } catch (error) {
    console.error('Error refreshing session:', error);
    return {
      isValid: false,
      isExpired: false,
      expiresAt: null,
      user: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get session timeout in seconds
 * Returns the time until session expires
 */
export function getSessionTimeout(expiresAt: number | null): number {
  if (!expiresAt) {
    return 0;
  }

  const now = Date.now() / 1000;
  const timeout = expiresAt - now;

  return Math.max(0, timeout);
}

/**
 * Check if session will expire soon (within 5 minutes)
 */
export function isSessionExpiringSoon(expiresAt: number | null): boolean {
  if (!expiresAt) {
    return false;
  }

  const timeout = getSessionTimeout(expiresAt);
  const fiveMinutes = 5 * 60; // 5 minutes in seconds

  return timeout <= fiveMinutes && timeout > 0;
}
