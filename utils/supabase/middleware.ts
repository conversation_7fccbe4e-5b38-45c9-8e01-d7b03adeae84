import { createServerClient } from "@supabase/ssr";
import { type NextRequest, NextResponse } from "next/server";

export const updateSession = async (request: NextRequest) => {
  try {
    // Create an unmodified response
    let response = NextResponse.next({
      request: {
        headers: request.headers,
      },
    });

    // Get the origin for site URL configuration
    const origin = request.headers.get('origin') || request.nextUrl.origin;
    console.log('Request origin:', origin);

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll();
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              request.cookies.set(name, value);
              response.cookies.set(name, value, options);
            });
          },
        },
        auth: {
          flowType: 'pkce',
          autoRefreshToken: true,
          detectSessionInUrl: true,
          persistSession: true,
          storageKey: 'wealthie-auth-token', // Match client storage key
        },
      },
    );

    // This will refresh session if expired - required for Server Components
    // https://supabase.com/docs/guides/auth/server-side/nextjs
    // SECURITY NOTE: Always use getUser() instead of getSession() for authentication checks
    // getUser() authenticates the data by contacting the Supabase Auth server
    // getSession() returns data directly from storage (cookies) which may not be authentic

    // SECURITY: Always use getUser() for authentication checks as it validates with the server
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    // Enhanced authentication and session validation for protected routes
    if (request.nextUrl.pathname.startsWith("/protected")) {
      // If there's no user, try to get session as fallback for timing issues
      if (userError || !user) {
        console.log('🔄 No user found, checking session as fallback...');

        try {
          const { data: { session }, error: sessionError } = await supabase.auth.getSession();

          if (session && session.user && !sessionError) {
            console.log('✅ Found valid session, allowing access');
            // Session exists, allow through
          } else {
            console.log('❌ No valid session found, redirecting to sign-in');
            return NextResponse.redirect(new URL("/sign-in", request.url));
          }
        } catch (fallbackError) {
          console.log('❌ Session fallback failed, redirecting to sign-in');
          return NextResponse.redirect(new URL("/sign-in", request.url));
        }
      }

      // Additional session integrity check
      try {
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError || !session) {
          return NextResponse.redirect(new URL("/sign-in", request.url));
        }

        // Check if session is expired
        if (session.expires_at && session.expires_at < Date.now() / 1000) {
          return NextResponse.redirect(new URL("/sign-in", request.url));
        }
      } catch (sessionValidationError) {
        console.error("Session validation error:", sessionValidationError);
        return NextResponse.redirect(new URL("/sign-in", request.url));
      }
    }

    // Basic MFA enforcement for protected routes
    if (request.nextUrl.pathname.startsWith("/protected") && !userError) {
      try {
        const { data: aalData } = await supabase.auth.mfa.getAuthenticatorAssuranceLevel();
        const { data: factorsData } = await supabase.auth.mfa.listFactors();

        const allFactors = factorsData ? [...(factorsData.totp || []), ...(factorsData.phone || [])] : [];
        const hasVerifiedFactors = allFactors.some(factor => factor.status === 'verified');

        // Check if MFA is required for sensitive routes
        const sensitiveRoutes = [
          '/protected/households/household',
          '/protected/settings',
          '/protected/profile'
        ];

        const isSensitiveRoute = sensitiveRoutes.some(route =>
          request.nextUrl.pathname.startsWith(route)
        );

        if (isSensitiveRoute && hasVerifiedFactors && aalData &&
            aalData.nextLevel === 'aal2' && aalData.currentLevel === 'aal1') {
          return NextResponse.redirect(new URL("/mfa-challenge", request.url));
        }
      } catch (error) {
        console.error("Error checking MFA enforcement:", error);
      }
    }

    // Redirect to protected area if authenticated and on home page
    if (request.nextUrl.pathname === "/" && !userError) {
      return NextResponse.redirect(new URL("/protected", request.url));
    }

    // Allow access to MFA setup and challenge pages for authenticated users
    if ((request.nextUrl.pathname === "/mfa-setup" || request.nextUrl.pathname === "/mfa-challenge") && userError) {
      return NextResponse.redirect(new URL("/sign-in", request.url));
    }

    // Allow access to reset-password page regardless of authentication status
    if (request.nextUrl.pathname === "/reset-password") {
      return response;
    }

    return response;
  } catch (e) {
    // If you are here, a Supabase client could not be created!
    // This is likely because you have not set up environment variables.
    // Check out http://localhost:3000 for Next Steps.
    return NextResponse.next({
      request: {
        headers: request.headers,
      },
    });
  }
};
