import { createBrowserClient } from "@supabase/ssr";

/**
 * Creates a Supabase client for browser usage with enhanced session management
 *
 * SECURITY NOTE: This client should only be used in client components.
 * Always use getUser() instead of getSession() for authentication checks.
 * Enhanced with improved session management for THREAT #12 mitigation.
 */
export const createClient = () => {
  // Create the client with enhanced session management configuration
  const client = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        flowType: 'pkce',
        autoRefreshToken: true,
        detectSessionInUrl: true,
        persistSession: true,
        // Enhanced session management settings
        storageKey: 'wealthie-auth-token',
        storage: typeof window !== 'undefined' ? window.localStorage : undefined,
        debug: false, // Disable debug for production
      },
      global: {
        headers: {
          'X-Client-Info': 'wealthie-web-app',
        },
      },
    }
  );

  // SECURITY: Enhanced session validation and monitoring
  // Always use getUser() instead of getSession() for authentication checks

  return client;
};

export const signOut = async () => {
  const supabase = createClient();
  await supabase.auth.signOut();
};
