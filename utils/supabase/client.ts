import { createBrowserClient } from "@supabase/ssr";

/**
 * Creates a Supabase client for browser usage
 *
 * SECURITY NOTE: This client should only be used in client components.
 * Always use getUser() instead of getSession() for authentication checks.
 */
export const createClient = () => {
  // Create the client with the suppressGetSessionWarning option
  const client = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        flowType: 'pkce',
        autoRefreshToken: true,
        detectSessionInUrl: true,
        persistSession: true,
      },
    }
  );

  // SECURITY: Removed warning suppression to ensure proper authentication validation
  // Always use getUser() instead of getSession() for authentication checks

  return client;
};

export const signOut = async () => {
  const supabase = createClient();
  await supabase.auth.signOut();
};
