/**
 * Session Status Indicator
 * 
 * Clean, subtle indicator that shows session health without interfering
 * with user experience. Provides security feedback and session management.
 */

'use client';

import { useState, useEffect } from 'react';
import { useSessionHealth } from '@/hooks/useFastAuth';
import { refreshSessionProactively, forceSessionValidation } from '@/utils/enhanced-session-manager';
import { Shield, ShieldAlert, ShieldCheck, ShieldX, RefreshCw, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from '@/components/ui/tooltip';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface SessionStatusIndicatorProps {
  position?: 'top-right' | 'bottom-right' | 'top-left' | 'bottom-left';
  showDetails?: boolean;
  className?: string;
}

export default function SessionStatusIndicator({
  position = 'top-right',
  showDetails = false,
  className = ''
}: SessionStatusIndicatorProps) {
  const healthStatus = useSessionHealth();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Format time until expiry
  const formatTimeUntilExpiry = (seconds: number): string => {
    if (seconds <= 0) return 'Expired';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  // Get status icon and color
  const getStatusDisplay = () => {
    switch (healthStatus.status) {
      case 'healthy':
        return {
          icon: ShieldCheck,
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          description: 'Session secure and active'
        };
      case 'warning':
        return {
          icon: ShieldAlert,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          description: 'Session will expire soon'
        };
      case 'critical':
        return {
          icon: ShieldX,
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          description: 'Session expiring or expired'
        };
      case 'invalid':
      default:
        return {
          icon: Shield,
          color: 'text-gray-400',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          description: 'Session status unknown'
        };
    }
  };

  // Handle session refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshSessionProactively();
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Session refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle status check
  const handleStatusCheck = async () => {
    setIsRefreshing(true);
    try {
      await forceSessionValidation();
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Session validation failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const statusDisplay = getStatusDisplay();
  const StatusIcon = statusDisplay.icon;

  // Position classes
  const positionClasses = {
    'top-right': 'top-4 right-4',
    'bottom-right': 'bottom-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-left': 'bottom-4 left-4'
  };

  return (
    <TooltipProvider>
      <div className={`fixed ${positionClasses[position]} z-50 ${className}`}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={`
                h-8 w-8 p-0 rounded-full border transition-all duration-200
                ${statusDisplay.bgColor} ${statusDisplay.borderColor}
                hover:scale-105 hover:shadow-md
                ${healthStatus.status === 'critical' ? 'animate-pulse' : ''}
              `}
            >
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="relative">
                    <StatusIcon className={`h-4 w-4 ${statusDisplay.color}`} />
                    {isRefreshing && (
                      <RefreshCw className="absolute -top-1 -right-1 h-3 w-3 animate-spin text-blue-500" />
                    )}
                  </div>
                </TooltipTrigger>
                <TooltipContent side="left">
                  <div className="text-center">
                    <p className="font-medium">{statusDisplay.description}</p>
                    {healthStatus.timeUntilExpiry > 0 && (
                      <p className="text-xs text-muted-foreground">
                        Expires in {formatTimeUntilExpiry(healthStatus.timeUntilExpiry)}
                      </p>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent align="end" className="w-64">
            <div className="px-3 py-2">
              <div className="flex items-center gap-2 mb-2">
                <StatusIcon className={`h-4 w-4 ${statusDisplay.color}`} />
                <span className="font-medium">Session Status</span>
              </div>
              
              <div className="space-y-1 text-sm text-muted-foreground">
                <p>{statusDisplay.description}</p>
                
                {healthStatus.timeUntilExpiry > 0 && (
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>Expires in {formatTimeUntilExpiry(healthStatus.timeUntilExpiry)}</span>
                  </div>
                )}
                
                {lastRefresh && (
                  <p className="text-xs">
                    Last checked: {lastRefresh.toLocaleTimeString()}
                  </p>
                )}
              </div>
            </div>

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={handleStatusCheck}
              disabled={isRefreshing}
              className="cursor-pointer"
            >
              <div className="flex items-center gap-2">
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                <span>Check Status</span>
              </div>
            </DropdownMenuItem>

            {healthStatus.status === 'warning' || healthStatus.status === 'critical' ? (
              <DropdownMenuItem
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="cursor-pointer"
              >
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  <span>Refresh Session</span>
                </div>
              </DropdownMenuItem>
            ) : null}

            {showDetails && (
              <>
                <DropdownMenuSeparator />
                <div className="px-3 py-2 text-xs text-muted-foreground">
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className="capitalize">{healthStatus.status}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Time left:</span>
                      <span>{formatTimeUntilExpiry(healthStatus.timeUntilExpiry)}</span>
                    </div>
                  </div>
                </div>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </TooltipProvider>
  );
}

/**
 * Minimal session indicator for header/navbar
 */
export function SessionIndicatorMini() {
  const healthStatus = useSessionHealth();
  const statusDisplay = getStatusDisplay(healthStatus.status);
  const StatusIcon = statusDisplay.icon;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`
            flex items-center gap-1 px-2 py-1 rounded-md text-xs
            ${statusDisplay.bgColor} ${statusDisplay.borderColor} border
          `}>
            <StatusIcon className={`h-3 w-3 ${statusDisplay.color}`} />
            <span className={statusDisplay.color}>
              {healthStatus.status === 'healthy' ? 'Secure' : 
               healthStatus.status === 'warning' ? 'Expiring' :
               healthStatus.status === 'critical' ? 'Critical' : 'Unknown'}
            </span>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-center">
            <p className="font-medium">{statusDisplay.description}</p>
            {healthStatus.timeUntilExpiry > 0 && (
              <p className="text-xs text-muted-foreground">
                Expires in {formatTimeUntilExpiry(healthStatus.timeUntilExpiry)}
              </p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Helper function for status display (extracted for reuse)
function getStatusDisplay(status: string) {
  switch (status) {
    case 'healthy':
      return {
        icon: ShieldCheck,
        color: 'text-green-500',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        description: 'Session secure and active'
      };
    case 'warning':
      return {
        icon: ShieldAlert,
        color: 'text-yellow-500',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200',
        description: 'Session will expire soon'
      };
    case 'critical':
      return {
        icon: ShieldX,
        color: 'text-red-500',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        description: 'Session expiring or expired'
      };
    case 'invalid':
    default:
      return {
        icon: Shield,
        color: 'text-gray-400',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
        description: 'Session status unknown'
      };
  }
}

function formatTimeUntilExpiry(seconds: number): string {
  if (seconds <= 0) return 'Expired';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m`;
}
