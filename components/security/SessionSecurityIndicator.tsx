/**
 * Session Security Indicator Component
 *
 * Displays session status, security warnings, and provides
 * session management controls for users.
 */

'use client';

import React, { useState } from 'react';
import { AlertTriangle, Clock, RefreshCw, LogOut, CheckCircle, XCircle, Settings, Shield } from 'lucide-react';
import { useSessionSecurity } from '@/hooks/useSessionSecurity';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface SessionSecurityIndicatorProps {
  // Show detailed session information
  showDetails?: boolean;
  // Show session controls (refresh, sign out)
  showControls?: boolean;
  // Compact mode for smaller displays
  compact?: boolean;
  // Custom className
  className?: string;
}

export function SessionSecurityIndicator({
  showDetails = false,
  showControls = true,
  compact = false,
  className = ''
}: SessionSecurityIndicatorProps) {
  const [showPopover, setShowPopover] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [isExtending, setIsExtending] = useState(false);

  const {
    isLoading,
    sessionInfo,
    isAuthenticated,
    timeUntilExpiry,
    timeUntilIdle,
    securityWarnings,
    forceValidation,
    clearSecurityWarnings,
    signOut,
    refreshSession,
    forceRefreshSession
  } = useSessionSecurity({
    enableMonitoring: true,
    enableIdleTimeout: true,
    enableAutoRefresh: true,
    onSessionInvalid: (reason) => {
      console.warn('Session invalid:', reason);
    },
    onSecurityIssue: (flags) => {
      console.warn('Security issue detected:', flags);
    }
  });

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <RefreshCw className="h-4 w-4 animate-spin text-gray-500" />
        {!compact && <span className="text-sm text-gray-500">Checking session...</span>}
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <AlertTriangle className="h-4 w-4 text-red-500" />
        {!compact && <span className="text-sm text-red-600">Session invalid</span>}
      </div>
    );
  }

  const getSessionStatus = () => {
    if (securityWarnings.length > 0) {
      return {
        color: 'red',
        icon: XCircle,
        text: 'Security Alert',
        message: 'Security issues detected - click to review and resolve'
      };
    }

    if (timeUntilExpiry && timeUntilExpiry < 900) { // Less than 15 minutes
      return {
        color: 'yellow',
        icon: Clock,
        text: 'Session Expiring',
        message: 'Your session will expire soon - click to extend'
      };
    }

    if (timeUntilIdle && timeUntilIdle < 300000) { // Less than 5 minutes
      return {
        color: 'yellow',
        icon: Clock,
        text: 'Idle Warning',
        message: 'Session will timeout due to inactivity'
      };
    }

    return {
      color: 'green',
      icon: Shield,
      text: 'All Systems Secure',
      message: ''
    };
  };

  const status = getSessionStatus();
  const StatusIcon = status.icon;

  const formatTime = (seconds: number | null): string => {
    if (!seconds) return 'Unknown';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatTimeMs = (milliseconds: number | null): string => {
    if (!milliseconds) return 'Unknown';

    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const SessionDetails = () => (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <span className="font-medium">Security Status</span>
        <Badge
          variant={status.color === 'green' ? 'default' : status.color === 'yellow' ? 'secondary' : 'destructive'}
        >
          {status.text}
        </Badge>
      </div>

      <div className="text-sm text-gray-600 mb-3">
        {status.message}
      </div>

      {sessionInfo && (
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Session Status:</span>
            <span className={isAuthenticated ? 'text-gray-600' : 'text-red-600'}>
              {isAuthenticated ? 'Active & Secure' : 'Invalid'}
            </span>
          </div>

          {timeUntilExpiry && (
            <div className="flex justify-between">
              <span className="text-gray-600">Expires in:</span>
              <span className={timeUntilExpiry < 900 ? 'text-yellow-600' : 'text-gray-600'}>
                {formatTime(timeUntilExpiry)}
              </span>
            </div>
          )}

          {timeUntilIdle && (
            <div className="flex justify-between">
              <span className="text-gray-600">Activity timeout:</span>
              <span className={timeUntilIdle < 300000 ? 'text-yellow-600' : 'text-gray-600'}>
                {formatTimeMs(timeUntilIdle)}
              </span>
            </div>
          )}

          <div className="flex justify-between">
            <span className="text-gray-600">Security Level:</span>
            <span className={securityWarnings.length === 0 ? 'text-gray-600' : 'text-red-600'}>
              {securityWarnings.length === 0 ? 'High Security' : 'Security Issues'}
            </span>
          </div>
        </div>
      )}

      {securityWarnings.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="font-medium text-red-600">Security Issues:</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                clearSecurityWarnings();
                setShowPopover(false);
              }}
              className="text-xs"
            >
              <Settings className="h-3 w-3 mr-1" />
              Fix Issues
            </Button>
          </div>
          {securityWarnings.map((warning, index) => (
            <Alert key={index} variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">
                      {warning.includes('Suspicious activity') && 'Unusual Activity Detected'}
                      {warning.includes('Multiple devices') && 'Multiple Device Access'}
                      {warning.includes('Location change') && 'Location Change Detected'}
                    </div>
                    <div className="text-xs mt-1">
                      {warning.includes('Suspicious activity') && 'Rapid actions detected - may be automated activity'}
                      {warning.includes('Multiple devices') && 'Your account is being accessed from multiple devices'}
                      {warning.includes('Location change') && 'Login detected from a new location'}
                    </div>
                    {warning.includes('Suspicious activity') && (
                      <span className="block text-xs mt-1 text-gray-600">
                        If you just logged in, this may be a false positive.
                      </span>
                    )}
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      {showControls && (
        <div className="space-y-2 pt-2 border-t">
          {securityWarnings.length === 0 ? (
            <div className="text-center">
              <div className="flex items-center justify-center text-green-600 mb-2">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">All Security Checks Passed</span>
              </div>
              <div className="text-xs text-gray-600 mb-3">
                Your session is secure and protected
              </div>
            </div>
          ) : (
            <div className="text-center">
              <div className="flex items-center justify-center text-red-600 mb-2">
                <XCircle className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">Security Issues Detected</span>
              </div>
              <div className="text-xs text-gray-600 mb-3">
                Click "Fix Issues" above to resolve security warnings
              </div>
            </div>
          )}

          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={isChecking}
              onClick={async () => {
                setIsChecking(true);
                try {
                  await forceValidation();
                  // Keep popover open to show updated status
                } catch (error) {
                  console.error('Error checking status:', error);
                } finally {
                  setIsChecking(false);
                }
              }}
              className="flex-1"
            >
              <RefreshCw className={`h-3 w-3 mr-1 ${isChecking ? 'animate-spin' : ''}`} />
              {isChecking ? 'Checking...' : 'Check Status'}
            </Button>

            <Button
              variant="outline"
              size="sm"
              disabled={isExtending}
              onClick={async () => {
                setIsExtending(true);
                try {
                  const success = await forceRefreshSession();
                  if (success) {
                    console.log('Session extended successfully');
                  } else {
                    console.warn('Failed to extend session');
                  }
                  // Keep popover open to show updated session time
                } catch (error) {
                  console.error('Error extending session:', error);
                } finally {
                  setIsExtending(false);
                }
              }}
              className="flex-1"
            >
              <Clock className={`h-3 w-3 mr-1 ${isExtending ? 'animate-spin' : ''}`} />
              {isExtending ? 'Extending...' : 'Extend Session'}
            </Button>

            <Button
              variant="destructive"
              size="sm"
              onClick={() => {
                signOut();
                setShowPopover(false);
              }}
              className="flex-1"
            >
              <LogOut className="h-3 w-3 mr-1" />
              Sign Out
            </Button>
          </div>
        </div>
      )}
    </div>
  );

  if (compact) {
    return (
      <Popover open={showPopover} onOpenChange={setShowPopover}>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="sm" className={`p-1 ${className}`}>
            <StatusIcon
              className={`h-4 w-4 ${
                status.color === 'green' ? 'text-green-500' :
                status.color === 'yellow' ? 'text-yellow-500' :
                'text-red-500'
              }`}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="end">
          <SessionDetails />
        </PopoverContent>
      </Popover>
    );
  }

  if (showDetails) {
    return (
      <div className={`p-4 border rounded-lg bg-white ${className}`}>
        <SessionDetails />
      </div>
    );
  }

  return (
    <Popover open={showPopover} onOpenChange={setShowPopover}>
      <PopoverTrigger asChild>
        <Button variant="ghost" className={`flex items-center space-x-2 ${className}`}>
          <StatusIcon
            className={`h-4 w-4 ${
              status.color === 'green' ? 'text-green-500' :
              status.color === 'yellow' ? 'text-yellow-500' :
              'text-red-500'
            }`}
          />
          <span className="text-sm">{status.text}</span>
          {securityWarnings.length > 0 && (
            <Badge variant="destructive" className="ml-1">
              {securityWarnings.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <SessionDetails />
      </PopoverContent>
    </Popover>
  );
}
