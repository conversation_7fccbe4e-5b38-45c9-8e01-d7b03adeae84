/**
 * Session Security Indicator Component
 *
 * Displays session status, security warnings, and provides
 * session management controls for users.
 */

'use client';

import React, { useState } from 'react';
import { AlertTriangle, Shield, Clock, Monitor, RefreshCw, LogOut } from 'lucide-react';
import { useSessionSecurity } from '@/hooks/useSessionSecurity';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface SessionSecurityIndicatorProps {
  // Show detailed session information
  showDetails?: boolean;
  // Show session controls (refresh, sign out)
  showControls?: boolean;
  // Compact mode for smaller displays
  compact?: boolean;
  // Custom className
  className?: string;
}

export function SessionSecurityIndicator({
  showDetails = false,
  showControls = true,
  compact = false,
  className = ''
}: SessionSecurityIndicatorProps) {
  const [showPopover, setShowPopover] = useState(false);

  const {
    isLoading,
    sessionInfo,
    isAuthenticated,
    timeUntilExpiry,
    timeUntilIdle,
    securityWarnings,
    forceValidation,
    clearSecurityWarnings,
    signOut,
    refreshSession
  } = useSessionSecurity({
    enableMonitoring: true,
    enableIdleTimeout: true,
    enableAutoRefresh: true,
    onSessionInvalid: (reason) => {
      console.warn('Session invalid:', reason);
    },
    onSecurityIssue: (flags) => {
      console.warn('Security issue detected:', flags);
    }
  });

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <RefreshCw className="h-4 w-4 animate-spin text-gray-500" />
        {!compact && <span className="text-sm text-gray-500">Checking session...</span>}
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <AlertTriangle className="h-4 w-4 text-red-500" />
        {!compact && <span className="text-sm text-red-600">Session invalid</span>}
      </div>
    );
  }

  const getSessionStatus = () => {
    if (securityWarnings.length > 0) {
      return { color: 'red', icon: AlertTriangle, text: 'Security Issues' };
    }

    if (timeUntilExpiry && timeUntilExpiry < 900) { // Less than 15 minutes
      return { color: 'yellow', icon: Clock, text: 'Expiring Soon' };
    }

    if (timeUntilIdle && timeUntilIdle < 300000) { // Less than 5 minutes
      return { color: 'yellow', icon: Clock, text: 'Idle Warning' };
    }

    return { color: 'green', icon: Shield, text: 'Secure' };
  };

  const status = getSessionStatus();
  const StatusIcon = status.icon;

  const formatTime = (seconds: number | null): string => {
    if (!seconds) return 'Unknown';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatTimeMs = (milliseconds: number | null): string => {
    if (!milliseconds) return 'Unknown';

    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const SessionDetails = () => (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <span className="font-medium">Session Status</span>
        <Badge
          variant={status.color === 'green' ? 'default' : status.color === 'yellow' ? 'secondary' : 'destructive'}
        >
          {status.text}
        </Badge>
      </div>

      {sessionInfo && (
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Session ID:</span>
            <span className="font-mono">{sessionInfo.sessionId}</span>
          </div>

          <div className="flex justify-between">
            <span className="text-gray-600">Device ID:</span>
            <span className="font-mono text-xs">{sessionInfo.deviceId}</span>
          </div>

          {timeUntilExpiry && (
            <div className="flex justify-between">
              <span className="text-gray-600">Expires in:</span>
              <span className={timeUntilExpiry < 900 ? 'text-yellow-600' : ''}>
                {formatTime(timeUntilExpiry)}
              </span>
            </div>
          )}

          {timeUntilIdle && (
            <div className="flex justify-between">
              <span className="text-gray-600">Idle timeout:</span>
              <span className={timeUntilIdle < 300000 ? 'text-yellow-600' : ''}>
                {formatTimeMs(timeUntilIdle)}
              </span>
            </div>
          )}
        </div>
      )}

      {securityWarnings.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="font-medium text-red-600">Security Warnings:</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                clearSecurityWarnings();
                setShowPopover(false);
              }}
              className="text-xs"
            >
              Clear Warnings
            </Button>
          </div>
          {securityWarnings.map((warning, index) => (
            <Alert key={index} variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {warning}
                {warning.includes('Suspicious activity') && (
                  <span className="block text-xs mt-1 text-gray-600">
                    This may be a false positive if you just logged in.
                  </span>
                )}
              </AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      {showControls && (
        <div className="flex space-x-2 pt-2 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              forceValidation();
              setShowPopover(false);
            }}
            className="flex-1"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Refresh
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              refreshSession();
              setShowPopover(false);
            }}
            className="flex-1"
          >
            <Clock className="h-3 w-3 mr-1" />
            Extend
          </Button>

          <Button
            variant="destructive"
            size="sm"
            onClick={() => {
              signOut();
              setShowPopover(false);
            }}
            className="flex-1"
          >
            <LogOut className="h-3 w-3 mr-1" />
            Sign Out
          </Button>
        </div>
      )}
    </div>
  );

  if (compact) {
    return (
      <Popover open={showPopover} onOpenChange={setShowPopover}>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="sm" className={`p-1 ${className}`}>
            <StatusIcon
              className={`h-4 w-4 ${
                status.color === 'green' ? 'text-green-500' :
                status.color === 'yellow' ? 'text-yellow-500' :
                'text-red-500'
              }`}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="end">
          <SessionDetails />
        </PopoverContent>
      </Popover>
    );
  }

  if (showDetails) {
    return (
      <div className={`p-4 border rounded-lg bg-white ${className}`}>
        <SessionDetails />
      </div>
    );
  }

  return (
    <Popover open={showPopover} onOpenChange={setShowPopover}>
      <PopoverTrigger asChild>
        <Button variant="ghost" className={`flex items-center space-x-2 ${className}`}>
          <StatusIcon
            className={`h-4 w-4 ${
              status.color === 'green' ? 'text-green-500' :
              status.color === 'yellow' ? 'text-yellow-500' :
              'text-red-500'
            }`}
          />
          <span className="text-sm">{status.text}</span>
          {securityWarnings.length > 0 && (
            <Badge variant="destructive" className="ml-1">
              {securityWarnings.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <SessionDetails />
      </PopoverContent>
    </Popover>
  );
}
