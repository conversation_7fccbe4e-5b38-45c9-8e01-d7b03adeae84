'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useFastAuth } from '@/hooks/useFastAuth';

interface RouteGuardProps {
  children: React.ReactNode;
}

export default function RouteGuard({ children }: RouteGuardProps) {
  const pathname = usePathname();
  const router = useRouter();

  // Add fast authentication validation only for protected routes
  const shouldCheckAuth = pathname.startsWith('/protected');
  const { isAuthenticated, isLoading, isInitialized } = useFastAuth({
    requireAuth: shouldCheckAuth,
    redirectTo: '/sign-in',
    enableAutoRefresh: true,
    onAuthChange: (user) => {
      // Log authentication changes for debugging
      console.log('RouteGuard: Auth change detected:', !!user, 'for path:', pathname);
    }
  });

  // ALL HOOKS MUST BE CALLED BEFORE ANY CONDITIONAL RETURNS
  useEffect(() => {
    // Define allowed routes
    const allowedRoutes = [
      '/protected/scenarios',
      '/protected/admin/profile',
      '/protected/admin/organisation',
      '/protected/households',
      '/protected/planner',
      '/protected/presentation'
    ];

    // Define allowed household detail routes
    const allowedHouseholdRoutes = [
      '/household_overview',
      '/income_expenses',
      '/assets_liabilities',
      '/scenarios'
    ];

    // Check if it's a household route and if it's allowed
    if (pathname.includes('/protected/households/household/')) {
      const householdId = pathname.split('/households/household/')[1].split('/')[0];
      const routeSuffix = pathname.split(`/households/household/${householdId}`)[1];

      // Allow the base household route
      if (!routeSuffix || routeSuffix === '') {
        return;
      }

      // Check if the route suffix is in the allowed list
      const isAllowedHouseholdRoute = allowedHouseholdRoutes.some(route =>
        routeSuffix === route || routeSuffix.startsWith(route)
      );

      if (!isAllowedHouseholdRoute) {
        router.replace(`/protected/households/household/${householdId}/household_overview`);
        return;
      }

      return;
    }

    // Check if current path is allowed
    const isAllowedRoute = allowedRoutes.some(route =>
      pathname.startsWith(route) || pathname === '/protected'
    );

    if (pathname.startsWith('/protected/') && !isAllowedRoute) {
      router.replace('/protected/scenarios');
    }
  }, [pathname, router]);

  // CONDITIONAL RENDERING AFTER ALL HOOKS
  // Show loading state only if not initialized and authentication is required
  if (shouldCheckAuth && isLoading && !isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Don't render protected content if not authenticated
  if (shouldCheckAuth && !isAuthenticated) {
    return null; // Will redirect via useFastAuth
  }

  return <>{children}</>;
}
