import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
// Removed unused Command imports
import { Check, ChevronsUpDown, AlertCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

interface CreateScenarioModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateScenario?: (householdId: string, scenarioName: string, userId: string, householdName: string | null, orgId?: string) => void;
  onSuccess?: () => void;
  preselectedHousehold?: { id: number; householdName: string };
}

interface Household {
  id: number;
  householdName: string;
}

export default function CreateScenarioModal({
  isOpen,
  onClose,
  onCreateScenario,
  onSuccess,
  preselectedHousehold
}: CreateScenarioModalProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [scenarioName, setScenarioName] = useState('');
  const [selectedHousehold, setSelectedHousehold] = useState('');
  const [selectedHouseholdName, setSelectedHouseholdName] = useState('');
  const [households, setHouseholds] = useState<Household[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMembers, setHasMembers] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [filteredHouseholds, setFilteredHouseholds] = useState<Household[]>([]);
  // const [viewMode, setViewMode] = useState<'user' | 'organization'>('user');
  const [profileData, setProfileData] = useState<{user_id?: string, org_id?: string}>({});
  const [scenarioMetrics, setScenarioMetrics] = useState<any>(null);
  const [isDuplicateName, setIsDuplicateName] = useState(false);

  useEffect(() => {
    const fetchCurrentUser = async () => {
      const supabase = createClient();
      const { data } = await supabase.auth.getUser();
      if (data.user) {
        setUserId(data.user.id);

        // Fetch user profile data
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('user_id, org_id')
          .eq('user_id', data.user.id)
          .single();

        if (!profileError && profileData) {
          setProfileData(profileData);

          // Fetch scenario metrics
          const { data: metricsData, error: metricsError } = await supabase
            .from('scenario_metrics')
            .select('*')
            .or(`user_id.eq.${data.user.id}${profileData.org_id ? `,org_id.eq.${profileData.org_id}` : ''}`)
            .order('created_at', { ascending: false })
            .limit(1);

          if (!metricsError && metricsData && metricsData.length > 0) {
            setScenarioMetrics(metricsData[0]);
          } else {
          }
        }
      }
    };

    fetchCurrentUser();
  }, []);

  useEffect(() => {
    const fetchHouseholds = async () => {
      if (preselectedHousehold) {
        setHouseholds([preselectedHousehold]);
        setFilteredHouseholds([preselectedHousehold]);
        setSelectedHousehold(preselectedHousehold.id.toString());
        setSelectedHouseholdName(preselectedHousehold.householdName);
        // Check if preselected household has members
        await checkHouseholdMembers(preselectedHousehold.id.toString());
        return;
      }

      const supabase = createClient();
      const { data, error } = await supabase
        .from('households')
        .select('id, householdName')
        .order('householdName', { ascending: true });

      if (error) {
        console.error('Error fetching households:', error);
      } else {
        setHouseholds(data || []);
        setFilteredHouseholds(data || []);
      }
      setIsLoading(false);
    };

    fetchHouseholds();
  }, [preselectedHousehold]);

  // Update filtered households when search term changes
  useEffect(() => {
    if (search.trim() === '') {
      setFilteredHouseholds(households);
    } else {
      const searchLower = search.toLowerCase();
      const filtered = households.filter(household => {
        const nameLower = household.householdName.toLowerCase();
        const includes = nameLower.includes(searchLower);
        return includes;
      });
      setFilteredHouseholds(filtered);
    }
  }, [search, households]);

  const checkHouseholdMembers = async (householdId: string) => {
    if (!householdId) return;

    setIsLoading(true);
    const supabase = createClient();
    const { data, error } = await supabase
      .from('households')
      .select('members, householdName')
      .eq('id', householdId)
      .single();

    if (error) {
      console.error('Error fetching household data:', error);
      setHasMembers(false);
    } else {
      const members = data.members;
      let memberExists = false;

      if (typeof members === 'string') {
        try {
          const parsedMembers = JSON.parse(members);
          memberExists = !!(parsedMembers.name1 || parsedMembers.name2);
        } catch (e) {
          console.error('Error parsing members:', e);
        }
      } else if (typeof members === 'object' && members !== null) {
        const membersObj = members as { name1?: string; name2?: string };
        memberExists = !!(membersObj.name1 || membersObj.name2);
      }

      setHasMembers(memberExists);
      setSelectedHouseholdName(data.householdName);
    }
    setIsLoading(false);
  };

  const handleHouseholdChange = (value: string) => {
    setSelectedHousehold(value);
    setOpen(false);
    const selected = households.find(h => h.id.toString() === value);
    if (selected) {
      setSelectedHouseholdName(selected.householdName);
    }
    checkHouseholdMembers(value);
    // Reset duplicate name check when household changes
    if (scenarioName) {
      checkDuplicateScenarioName(scenarioName, value);
    }
  };

  // Function to check if a scenario with the same name already exists for this household
  const checkDuplicateScenarioName = async (name: string, householdId: string = selectedHousehold) => {
    if (!name.trim() || !householdId) {
      setIsDuplicateName(false);
      return;
    }

    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('scenarios_data1')
        .select('id')
        .eq('scenario_name', name.trim())
        .eq('household_id', householdId);

      if (error) {
        console.error('Error checking for duplicate scenario name:', error);
        return;
      }

      setIsDuplicateName(data && data.length > 0);
    } catch (error) {
      console.error('Error in checkDuplicateScenarioName:', error);
    }
  };

  // Helper function to annualize amounts based on frequency
  const getAnnualizedAmount = (amount: number, frequency: string): number => {
    if (isNaN(amount) || amount === null || amount === undefined) {
      return 0;
    }

    const freqLower = frequency?.toLowerCase() || 'annually';
    switch (freqLower) {
      case 'weekly':
        return amount * 52;
      case 'fortnightly':
        return amount * 26;
      case 'monthly':
        return amount * 12;
      case 'quarterly':
        return amount * 4;
      case 'annually':
      default: // Treat unrecognized frequencies as annual
        return amount;
    }
  };

  // Helper function to get default values from scenario metrics
  const getDefaultValue = (category: string, field: string, fallbackValue: any): any => {
    if (!scenarioMetrics || !scenarioMetrics.default_values) {
      return fallbackValue;
    }

    try {
      const defaultValues = typeof scenarioMetrics.default_values === 'string'
        ? JSON.parse(scenarioMetrics.default_values)
        : scenarioMetrics.default_values;

      if (defaultValues[category] && defaultValues[category][field] && defaultValues[category][field].value !== undefined) {
        return defaultValues[category][field].value;
      }
    } catch (error) {
      console.error('Error parsing default values:', error);
    }

    return fallbackValue;
  };

  // This is now the "Open in Scenario Builder" function
  const handleCreateUsingBuilder = async () => {
    if (isFormValid) {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('households')
        .select('*')
        .eq('id', selectedHousehold)
        .single();

      if (error) {
        console.error('Error fetching household data:', error);
        return;
      }

      const householdData = data.members || {};
      const scenarioData = {
        householdId: selectedHousehold,
        name: scenarioName,
        userId: userId,
        org_id: profileData.org_id || null,
        household_name: data.householdName || null, // Add household name here
        personal: {
          name: householdData.name1 || '',
          starting_age: parseInt(householdData.age1) || null,
          ending_age: null,
          include_partner: !!householdData.name2,
          partner_name: householdData.name2 || '',
          partner_starting_age: parseInt(householdData.age2) || null,
        },
        income: {
          annual_income: parseFloat(householdData.income1) || null,
          income_period: [null, null],
          include_superannuation: false,
          partner_annual_income: parseFloat(householdData.income2) || null,
          partner_income_period: [null, null],
        },
        savings: {
          savings_amount: parseFloat(householdData.savingsValue) || null,
          cash_reserve: null,
          saving_percentage: null,
        },
        expenditure: {
          annual_expenses1: parseFloat(householdData.annualExpenses) || null,
          expense_period1: [null, null],
          annual_expenses2: null,
          expense_period2: [null, null],
        },
        investment: {
          initial_investment: parseFloat(householdData.investmentValue) || null,
          annual_investment_contribution: null,
          annual_investment_return: null,
          inv_std_dev: null,
        },
        kiwiSaver: {
          initial_kiwisaver: parseFloat(householdData.kiwisaverValue1) || null,
          kiwisaver_contribution: parseFloat(householdData.kiwisaverContribution1) || null,
          employer_contribution: parseFloat(householdData.employerContribution1) || null,
          annual_kiwisaver_return: null,
          ks_std_dev: null,
          partner_initial_kiwisaver: parseFloat(householdData.kiwisaverValue2) || null,
          partner_kiwisaver_contribution: parseFloat(householdData.kiwisaverContribution2) || null,
          partner_employer_contribution: parseFloat(householdData.employerContribution2) || null,
          partner_annual_kiwisaver_return: null,
          partner_ks_std_dev: null,
        },
        property: {
          property_value: parseFloat(householdData.propertyValue) || null,
          debt: parseFloat(householdData.debtValue) || null,
          property_growth: null,
          debt_ir: null,
          initial_debt_years: null,
          additional_debt_repayments: null,
          include_property_debt: false,
        },
        economic: {
          inflation_rate: null
        },
        monteCarlo: {
          simulations: null,
          confidence_interval: null,
        },
        misc: {
          household_name: data.householdName || null,
        },
      };

      const queryParams = new URLSearchParams({
        data: JSON.stringify(scenarioData)
      }).toString();

      router.push(`/protected/scenario-builder?${queryParams}`);
      onClose();
      if (onSuccess) {
        onSuccess();
      }
    }
  };

  // This is now the "Create Scenario Automatically" function
  const handleCreateFromData = async () => {
    if (isFormValid) {
      setIsLoading(true);

      try {
        const supabase = createClient();

        // 1. Fetch household data including members with specific column selection
        const { data: householdData, error: householdError } = await supabase
          .from('households')
          .select('id, householdName, members, city, state, created_at')
          .eq('id', selectedHousehold)
          .single();

        if (householdError) throw householdError;

        // 2. Fetch income, expense, and asset data with specific column selection
        const { data: incomeData, error: incomeError } = await supabase
          .from('income')
          .select('id, amount, frequency, source, description, household_id, created_at, member_id, income_type, details, start_date, end_date, is_guaranteed, linked_asset_id')
          .eq('household_id', selectedHousehold);

        if (incomeError) throw incomeError;

        const { data: expenseData, error: expenseError } = await supabase
          .from('expenses')
          .select('id, amount, frequency, category, description, household_id, created_at, is_essential, notes, linked_liability_id')
          .eq('household_id', selectedHousehold);

        if (expenseError) throw expenseError;

        // Fetch all assets and their liabilities
        const { data: assetData, error: assetError } = await supabase
          .from('assets')
          .select('*, liabilities(*)')
          .eq('household_id', selectedHousehold);

        if (assetError) throw assetError;

        // Filter assets by type
        const propertyAssets = assetData && Array.isArray(assetData)
          ? assetData.filter(asset => asset.type === 'property')
          : [];

        const investmentAssets = assetData && Array.isArray(assetData)
          ? assetData.filter(asset => asset.type === 'investment')
          : [];

        const kiwisaverAssets = assetData && Array.isArray(assetData)
          ? assetData.filter(asset => asset.type === 'kiwisaver')
          : [];

        const superannuationAssets = assetData && Array.isArray(assetData)
          ? assetData.filter(asset => asset.type === 'superannuation')
          : [];

        const savingsAssets = assetData && Array.isArray(assetData)
          ? assetData.filter(asset => asset.type === 'savings')
          : [];

        // Log asset counts for debugging

        // Get the main property (owner_occupied) or the first property if no owner_occupied exists
        const mainProperty = propertyAssets.find(property => property.property_type === 'owner_occupied') ||
                            (propertyAssets.length > 0 ? propertyAssets[0] : null);

        // Get additional properties (excluding the main property)
        const additionalProperties = mainProperty
          ? propertyAssets.filter(property => property.id !== mainProperty.id)
          : [];

        // Process investment assets to create individual investment funds
        const investmentFunds = investmentAssets.map((asset, index) => ({
          fundNumber: index + 1,
          name: asset.name,
          value: parseFloat(asset.value || '0'),
          provider: asset.provider || '',
          details: asset.details || '',
          member_id: asset.member_id || null
        }));

        // Calculate total investment value
        const totalInvestmentValue = investmentAssets.reduce((sum, asset) =>
          sum + parseFloat(asset.value || '0'), 0);

        // Log investment assets for debugging

        // Log investment funds for debugging

        // Calculate total savings value
        const totalSavingsValue = savingsAssets.reduce((sum, asset) =>
          sum + parseFloat(asset.value || '0'), 0);

        // Log savings assets for debugging

        // Find KiwiSaver accounts for each member
        const member1KiwiSaver = kiwisaverAssets.find(asset => asset.member_id === '1') ||
                                superannuationAssets.find(asset => asset.member_id === '1');

        const member2KiwiSaver = kiwisaverAssets.find(asset => asset.member_id === '2') ||
                                superannuationAssets.find(asset => asset.member_id === '2');

        // Log KiwiSaver details for debugging

        // 3. Process income and expense data
        const members = householdData.members || {};
        const primaryIncomes: any[] = [];
        const additionalIncomes: any[] = [];
        const propertyIncomes: any[] = [];
        const propertyExpenses: any[] = [];
        const regularExpenses: any[] = [];

        // Process incomes to separate primary from additional and property-linked
        if (incomeData && incomeData.length > 0) {
          incomeData.forEach(income => {
            // Skip incomes linked to properties - these will be handled separately
            if (income.linked_asset_id) {
              propertyIncomes.push(income);
              return;
            }

            // Logic to determine primary vs additional income
            // Primary incomes are those with member_id 1 or 2 (main or partner)
            // or those with income_type = 'salary' or 'primary'
            if (income.member_id === 1 || income.member_id === 2 ||
                income.income_type === 'salary' || income.income_type === 'primary') {
              // If we already have a primary income for this member, add as additional
              const existingPrimaryForMember = primaryIncomes.find(pi => pi.member_id === income.member_id);
              if (existingPrimaryForMember) {
                additionalIncomes.push(income);
              } else {
                primaryIncomes.push(income);
              }
            } else {
              additionalIncomes.push(income);
            }
          });
        }

        // Sort primary incomes by member_id to ensure member 1 is first
        primaryIncomes.sort((a, b) => (a.member_id || 0) - (b.member_id || 0));

        // Process expenses to separate property-linked from regular
        if (expenseData && expenseData.length > 0) {
          expenseData.forEach(expense => {
            // Skip expenses linked to properties - these will be handled separately
            if (expense.linked_liability_id) {
              propertyExpenses.push(expense);
              return;
            }

            regularExpenses.push(expense);
          });
        }

        // 4. Create scenario data object
        const scenarioData = {
          scenario_name: scenarioName,
          household_id: parseInt(selectedHousehold),
          user_id: userId,
          org_id: profileData.org_id || null,
          household_name: householdData.householdName || null,

          // Personal data
          name: members.name1 || '',
          starting_age: parseInt(members.age1) || 30,
          ending_age: 95, // Always set to 95 as requested

          // Partner data if exists
          include_partner: !!members.name2,
          partner_name: members.name2 || '',
          partner_starting_age: parseInt(members.age2) || null,

          // Income data - use primary incomes first
          annual_income: primaryIncomes && Array.isArray(primaryIncomes) && primaryIncomes.length > 0 && primaryIncomes[0].member_id === 1 ?
            getAnnualizedAmount(parseFloat(primaryIncomes[0].amount || '0'), primaryIncomes[0].frequency || 'annually') :
            parseFloat(members.income1 || '0') || 0,

          income_period: [parseInt(members.age1 || '30') || 30, 64], // Working age to retirement

          // Partner income if exists
          partner_annual_income: primaryIncomes && Array.isArray(primaryIncomes) && primaryIncomes.length > 1 && primaryIncomes[1].member_id === 2 ?
            getAnnualizedAmount(parseFloat(primaryIncomes[1].amount || '0'), primaryIncomes[1].frequency || 'annually') :
            parseFloat(members.income2 || '0') || 0,

          partner_income_period: [parseInt(members.age2 || '30') || 30, 64], // Working age to retirement

          // Additional incomes - format exactly as expected by the planner
          additional_incomes:
            additionalIncomes && Array.isArray(additionalIncomes) ?
              additionalIncomes.map(income => {
                // Determine tax type based on member_id
                let taxType = 'tax_free'; // Default to tax_free for household-level incomes
                if (income.member_id === 1) {
                  taxType = 'main';
                } else if (income.member_id === 2) {
                  taxType = 'partner';
                }

                // Annualize the amount based on frequency
                const annualizedAmount = getAnnualizedAmount(
                  parseFloat(income.amount || '0'),
                  income.frequency || 'annually'
                );

                return {
                  title: income.source || 'Additional Income',
                  value: annualizedAmount,
                  period: [parseInt(members.age1 || '30') || 30, 95], // Full period
                  tax_type: taxType,
                  inflation_rate: 2.75 // Add inflation rate as shown in the example
                };
              }) :
              [],

          // Expense data - only use regular expenses (not property-linked)
          annual_expenses1: regularExpenses && Array.isArray(regularExpenses) && regularExpenses.length > 0 ?
            regularExpenses.reduce((total, expense) =>
              total + getAnnualizedAmount(parseFloat(expense.amount || '0'), expense.frequency || 'annually'), 0) :
            parseFloat(members.annualExpenses || '0') || 0,

          expense_period1: [parseInt(members.age1 || '30') || 30, 64], // Pre-retirement
          annual_expenses2: regularExpenses && Array.isArray(regularExpenses) && regularExpenses.length > 0 ?
            regularExpenses.reduce((total, expense) =>
              total + getAnnualizedAmount(parseFloat(expense.amount || '0'), expense.frequency || 'annually'), 0) :
            parseFloat(members.annualExpenses || '0') || 0, // Same as pre-retirement

          expense_period2: [65, 95], // Post-retirement

          // Other financial data - use investment assets, savings assets, and KiwiSaver values
          savings_amount: totalSavingsValue > 0 ?
            totalSavingsValue :
            parseFloat(members.savingsValue) || 0,

          // Set initial_investment to 0 since we'll be using the investment_metrics JSONB field
          initial_investment: 0,

          // Create investment_metrics JSONB structure
          investment_metrics: {
            funds: investmentFunds.length > 0 ?
              // Create fund objects for each investment asset
              investmentFunds.reduce((acc, fund) => {
                const fundNumber = fund.fundNumber;
                return {
                  ...acc,
                  [`fund${fundNumber}`]: {
                    fund_index: fundNumber,
                    initial_investment: fund.value,
                    investment_description: fund.name,
                    annual_investment_return: getDefaultValue('investment', 'annual_investment_return', 5.5),
                    inv_std_dev: getDefaultValue('investment', 'inv_std_dev', 8.0),
                    inv_tax: "PIE",
                    fund_periods: [],
                    contribution_period: [parseInt(members.age1 || '30') || 30, 95],
                    investment_return_period: [parseInt(members.age1 || '30') || 30, 65],
                    annual_investment_contribution: 0
                  }
                };
              }, {}) :
              // If no investment assets but we have a total investment value, create a single fund
              totalInvestmentValue > 0 ? {
                fund1: {
                  fund_index: 1,
                  initial_investment: totalInvestmentValue,
                  investment_description: 'Investment Fund',
                  annual_investment_return: getDefaultValue('investment', 'annual_investment_return', 5.5),
                  inv_std_dev: getDefaultValue('investment', 'inv_std_dev', 8.0),
                  inv_tax: "PIE",
                  fund_periods: [],
                  contribution_period: [parseInt(members.age1 || '30') || 30, 95],
                  investment_return_period: [parseInt(members.age1 || '30') || 30, 65],
                  annual_investment_contribution: 0
                }
              } : {},
            legacy: {
              inv_tax: "PIE",
              fund_type: "Balanced",
              inv_std_dev: getDefaultValue('investment', 'inv_std_dev', 8.0),
              initial_investment: 0,
              contribution_period: [parseInt(members.age1 || '30') || 30, 95],
              investment_description: null,
              annual_investment_return: getDefaultValue('investment', 'annual_investment_return', 5.5),
              investment_return_period: [parseInt(members.age1 || '30') || 30, 65],
              annual_investment_contribution: 0
            },
            one_off_investments: [],
            withdrawal_priorities: investmentFunds.length > 0 ?
              investmentFunds.map(fund => fund.fundNumber) :
              (totalInvestmentValue > 0 ? [1] : [])
          },

          // Use member 1's KiwiSaver value if available
          initial_kiwisaver: member1KiwiSaver ?
            parseFloat(member1KiwiSaver.value || '0') :
            parseFloat(members.kiwisaverValue1) || 0,

          kiwisaver_contribution: parseFloat(members.kiwisaverContribution1) || getDefaultValue('kiwisaver', 'kiwisaver_contribution', 3),
          employer_contribution: parseFloat(members.employerContribution1) || getDefaultValue('kiwisaver', 'employer_contribution', 3),
          consolidate_kiwisaver: getDefaultValue('kiwisaver', 'consolidate_kiwisaver', false),
          consolidate_kiwisaver_age: null, // Default if not specified

          // Fund type for KiwiSaver if available
          fund_type: member1KiwiSaver?.fund_type || null,

          // Partner KiwiSaver if exists - use member 2's KiwiSaver value if available
          partner_initial_kiwisaver: member2KiwiSaver ?
            parseFloat(member2KiwiSaver.value || '0') :
            parseFloat(members.kiwisaverValue2) || 0,

          partner_kiwisaver_contribution: parseFloat(members.kiwisaverContribution2) || getDefaultValue('kiwisaver', 'kiwisaver_contribution', 3),
          partner_employer_contribution: parseFloat(members.employerContribution2) || getDefaultValue('kiwisaver', 'employer_contribution', 3),
          partner_consolidate_kiwisaver: getDefaultValue('kiwisaver', 'consolidate_kiwisaver', false),
          partner_consolidate_kiwisaver_age: null, // Default if not specified

          // Property data - set property_value to 0 to exclude from net wealth
          property_value: 0, // Force to 0 to exclude from net wealth calculation
          debt: mainProperty && mainProperty.liabilities && Array.isArray(mainProperty.liabilities) && mainProperty.liabilities.length > 0 ?
            parseFloat(mainProperty.liabilities[0].amount || '0') :
            parseFloat(members.debtValue || '0') || 0,
          debt_ir: mainProperty && mainProperty.liabilities && Array.isArray(mainProperty.liabilities) && mainProperty.liabilities.length > 0 ?
            parseFloat(mainProperty.liabilities[0].interest_rate || '5.0') || 5.0 :
            5.0,
          initial_debt_years: 28, // Default to 28 years as requested

          // Rental income for main property if it exists
          rental_income: mainProperty ?
            (mainProperty.rental_income > 0 || !!propertyIncomes.find(income => income.linked_asset_id === mainProperty.id)) :
            getDefaultValue('property', 'rental_income', false),
          rental_amount: mainProperty ?
            (mainProperty.rental_income > 0 ?
              parseFloat(mainProperty.rental_income || '0') :
              (() => {
                const rentalIncome = propertyIncomes.find(income => income.linked_asset_id === mainProperty.id);
                const amount = parseFloat(rentalIncome?.amount || '0') || 0;
                return rentalIncome && rentalIncome.frequency && rentalIncome.frequency !== 'annually' ?
                  getAnnualizedAmount(amount, rentalIncome.frequency) :
                  amount;
              })()) :
            getDefaultValue('property', 'rental_income', 0),
          rental_start_age: parseInt(members.age1 || '30') || 30,
          rental_end_age: 95,

          // Board income
          board_income: getDefaultValue('property', 'board_income', false),
          board_amount: getDefaultValue('property', 'board_amount', null),
          board_start_age: null,
          board_end_age: null,

          // Interest only period
          interest_only_period: getDefaultValue('property', 'interest_only_period', false),
          interest_only_start_age: null,
          interest_only_end_age: null,

          // Additional properties - format exactly as in the SQL example
          additional_properties:
            additionalProperties.length > 0 ?
              additionalProperties.map((property, index) => {
                // Find income linked to this property
                const rentalIncome = propertyIncomes.find(income => income.linked_asset_id === property.id);
                // Find expenses linked to this property
                const propertyLiability = property.liabilities && Array.isArray(property.liabilities) && property.liabilities.length > 0 ?
                  property.liabilities[0] : null;

                // Check if property has rental income directly in the asset record
                const hasRentalIncome = property.rental_income > 0 || !!rentalIncome;
                const rentalAmount = property.rental_income > 0 ?
                  parseFloat(property.rental_income || '0') :
                  (rentalIncome ? parseFloat(rentalIncome.amount || '0') : 0);

                // Annualize the rental amount if needed
                const annualizedRentalAmount = rentalIncome && rentalIncome.frequency && rentalIncome.frequency !== 'annually' ?
                  getAnnualizedAmount(rentalAmount, rentalIncome.frequency) :
                  rentalAmount;

                return {
                  id: index + 1,
                  name: property.name || `Property ${index + 1}`,
                  value: 0, // Force to 0 to exclude from net wealth calculation
                  debt: propertyLiability ? parseFloat(propertyLiability.amount || '0') || 0 : 0,
                  debt_ir: propertyLiability ? parseFloat(propertyLiability.interest_rate || '5.0') || 5.0 : 5.0,
                  initial_debt_years: 28, // Default to 28 years
                  property_growth: getDefaultValue('property', 'property_inflation_rate', 3.0),
                  rental_income: hasRentalIncome,
                  rental_amount: annualizedRentalAmount,
                  rental_start_age: parseInt(members.age1 || '30') || 30,
                  rental_end_age: 95,
                  include_property_debt: getDefaultValue('property', 'include_property_debt', true)
                };
              }) :
              [],

          // Standard economic assumptions - use default values from scenario metrics
          inflation_rate: getDefaultValue('misc', 'inflation_rate', 2.0),
          property_growth: getDefaultValue('property', 'property_inflation_rate', 3.0),
          annual_investment_return: 5.0, // Default if not in metrics
          annual_kiwisaver_return: 5.0, // Default if not in metrics
          partner_annual_kiwisaver_return: 5.0, // Default if not in metrics

          // Explicitly set property-related display flags to false
          show_property_value: false,
          show_realistic_netwealth: false,
          exclude_properties_from_netwealth: true, // Special flag to indicate we want to exclude properties

          // Additional required fields - use default values from scenario metrics
          include_superannuation: getDefaultValue('income', 'include_superannuation', false),
          cash_reserve: getDefaultValue('savings', 'cash_reserve', 0),
          saving_percentage: getDefaultValue('savings', 'saving_percentage', 0),
          second_expense: getDefaultValue('expense', 'second_expense', false),
          annual_investment_contribution: 0, // Default if not in metrics
          inv_std_dev: 0, // Default if not in metrics
          ks_std_dev: 0, // Default if not in metrics
          partner_ks_std_dev: 0, // Default if not in metrics
          additional_debt_repayments: 0, // Default if not in metrics
          include_property_debt: false, // Force to false to exclude property debt from calculations
          simulations: getDefaultValue('misc', 'num_simulations', 1000),
          confidence_interval: getDefaultValue('misc', 'confidence_interval', 95),

          // Investment allocation settings
          allocate_to_investment: getDefaultValue('investment', 'allocate_to_investment', false),
          utilise_excess_cashflow: getDefaultValue('investment', 'utilise_excess_cashflow', false),

          // Income inflation rates
          main_income_inflation_rate: getDefaultValue('income', 'income_inflation_rate', null),
          partner_income_inflation_rate: getDefaultValue('income', 'partner_income_inflation_rate', null),

          // Expense inflation rates
          expense1_inflation_rate: getDefaultValue('expense', 'expense1_inflation_rate', null),
          expense2_inflation_rate: getDefaultValue('expense', 'expense2_inflation_rate', null),

          // Properties data in structured format - exactly as expected by the planner
          properties_data: [
            // Main property
            ...(mainProperty ? [{
              debt: mainProperty.liabilities && Array.isArray(mainProperty.liabilities) && mainProperty.liabilities.length > 0 ?
                parseFloat(mainProperty.liabilities[0].amount || '0') || 0 : 0,
              debt_ir: mainProperty.liabilities && Array.isArray(mainProperty.liabilities) && mainProperty.liabilities.length > 0 ?
                parseFloat(mainProperty.liabilities[0].interest_rate || '5.0') || 5.0 : 5.0,
              downsize: false,
              board_amount: null,
              board_income: false,
              downsize_age: null,
              pay_off_debt: false,
              board_end_age: null,
              rental_amount: mainProperty.rental_income > 0 ?
                parseFloat(mainProperty.rental_income || '0') :
                (() => {
                  const rentalIncome = propertyIncomes.find(income => income.linked_asset_id === mainProperty.id);
                  const amount = parseFloat(rentalIncome?.amount || '0') || 0;
                  return rentalIncome && rentalIncome.frequency && rentalIncome.frequency !== 'annually' ?
                    getAnnualizedAmount(amount, rentalIncome.frequency) :
                    amount;
                })(),
              rental_income: mainProperty.rental_income > 0 || !!propertyIncomes.find(income => income.linked_asset_id === mainProperty.id),
              property_index: 1,
              property_title: mainProperty.name || "",
              property_value: parseFloat(mainProperty.value || '0') || 0,
              net_wealth_value: 0, // Set to 0 to exclude from net wealth calculation
              rental_end_age: 95,
              board_start_age: null,
              property_growth: getDefaultValue('property', 'property_inflation_rate', 3.0),
              rental_start_age: parseInt(members.age1 || '30') || 30,
              initial_debt_years: 28, // Default to 28 years as requested
              include_in_net_wealth: false, // Exclude all properties from net wealth
              new_property_value: null,
              sell_main_property: false,
              interest_only_period: false,
              lump_sum_payment_age: null,
              main_prop_sale_value: null,
              interest_only_end_age: null,
              allocate_to_investment: false,
              main_property_sale_age: null,
              interest_only_start_age: null,
              lump_sum_payment_amount: null,
              lump_sum_payment_source: null,
              additional_debt_repayments: null,
              sale_allocate_to_investment: false,
              additional_debt_repayments_end_age: null,
              additional_debt_repayments_start_age: null
            }] : []),

            // Additional properties
            ...additionalProperties.map((property, index) => {
              const rentalIncome = propertyIncomes.find(income => income.linked_asset_id === property.id);
              const propertyLiability = property.liabilities && Array.isArray(property.liabilities) && property.liabilities.length > 0 ?
                property.liabilities[0] : null;

              const hasRentalIncome = property.rental_income > 0 || !!rentalIncome;
              const rentalAmount = property.rental_income > 0 ?
                parseFloat(property.rental_income || '0') :
                (rentalIncome ? parseFloat(rentalIncome.amount || '0') : 0);

              // Annualize the rental amount if needed
              const annualizedRentalAmount = rentalIncome && rentalIncome.frequency && rentalIncome.frequency !== 'annually' ?
                getAnnualizedAmount(rentalAmount, rentalIncome.frequency) :
                rentalAmount;

              return {
                debt: propertyLiability ? parseFloat(propertyLiability.amount || '0') || 0 : 0,
                debt_ir: propertyLiability ? parseFloat(propertyLiability.interest_rate || '5.0') || 5.0 : 5.0,
                downsize: false,
                board_amount: null,
                board_income: false,
                downsize_age: null,
                pay_off_debt: true,
                board_end_age: null,
                rental_amount: annualizedRentalAmount,
                rental_income: hasRentalIncome,
                property_index: index + 2, // Start from 2 for additional properties
                property_title: property.name || `Property ${index + 1}`,
                property_value: parseFloat(property.value || '0') || 0,
                net_wealth_value: 0, // Set to 0 to exclude from net wealth calculation
                rental_end_age: 95,
                board_start_age: null,
                property_growth: getDefaultValue('property', 'property_inflation_rate', 3.0),
                rental_start_age: parseInt(members.age1 || '30') || 30,
                initial_debt_years: 28, // Default to 28 years as requested
                include_in_net_wealth: false, // Exclude all properties from net wealth
                new_property_value: null,
                sell_main_property: false,
                interest_only_period: false,
                lump_sum_payment_age: null,
                main_prop_sale_value: null,
                interest_only_end_age: null,
                allocate_to_investment: false,
                main_property_sale_age: null,
                interest_only_start_age: null,
                lump_sum_payment_amount: null,
                lump_sum_payment_source: null,
                additional_debt_repayments: null,
                sale_allocate_to_investment: false,
                additional_debt_repayments_end_age: null,
                additional_debt_repayments_start_age: null
              };
            })
          ],

          // Creation metadata
          created_at: new Date().toISOString(),
          last_edited_at: new Date().toISOString(),
          last_viewed_at: new Date().toISOString()
        };

        // Log the scenario data for debugging

        // Log the actual scenario data being sent to the database

        // Log the raw data for debugging

        // Final check to ensure properties_data is not empty
        if (Array.isArray(scenarioData.properties_data) && scenarioData.properties_data.length === 0 && additionalProperties.length > 0) {
          // Add the properties manually as a fallback
          scenarioData.properties_data = [
            // Main property
            {
              debt: 0,
              debt_ir: 5.0,
              downsize: false,
              board_amount: null,
              board_income: false,
              downsize_age: null,
              pay_off_debt: false,
              board_end_age: null,
              rental_amount: 0,
              rental_income: false,
              property_index: 1,
              property_title: mainProperty?.name || "Main Property",
              property_value: parseFloat(mainProperty?.value || '0') || 0,
              net_wealth_value: 0, // Set to 0 to exclude from net wealth calculation
              rental_end_age: 95,
              board_start_age: null,
              property_growth: getDefaultValue('property', 'property_inflation_rate', 3.0),
              rental_start_age: parseInt(members.age1 || '30') || 30,
              initial_debt_years: 28, // Default to 28 years as requested
              include_in_net_wealth: false, // Exclude all properties from net wealth
              new_property_value: null,
              sell_main_property: false,
              interest_only_period: false,
              lump_sum_payment_age: null,
              main_prop_sale_value: null,
              interest_only_end_age: null,
              allocate_to_investment: false,
              main_property_sale_age: null,
              interest_only_start_age: null,
              lump_sum_payment_amount: null,
              lump_sum_payment_source: null,
              additional_debt_repayments: null,
              sale_allocate_to_investment: false,
              additional_debt_repayments_end_age: null,
              additional_debt_repayments_start_age: null
            },
            // Additional properties
            ...additionalProperties.map((property, index) => {
              const rentalIncome = propertyIncomes.find(income => income.linked_asset_id === property.id);
              const propertyLiability = property.liabilities && Array.isArray(property.liabilities) && property.liabilities.length > 0 ?
                property.liabilities[0] : null;

              return {
                debt: propertyLiability ? parseFloat(propertyLiability.amount || '0') || 0 : 0,
                debt_ir: propertyLiability ? parseFloat(propertyLiability.interest_rate || '5.0') || 5.0 : 5.0,
                downsize: false,
                board_amount: null,
                board_income: false,
                downsize_age: null,
                pay_off_debt: true,
                board_end_age: null,
                rental_amount: property.rental_income > 0 ?
                  parseFloat(property.rental_income || '0') :
                  (rentalIncome ? parseFloat(rentalIncome.amount || '0') : 0),
                rental_income: property.rental_income > 0 || !!rentalIncome,
                property_index: index + 2,
                property_title: property.name || `Property ${index + 1}`,
                property_value: parseFloat(property.value || '0') || 0,
                net_wealth_value: 0, // Set to 0 to exclude from net wealth calculation
                rental_end_age: 95,
                board_start_age: null,
                property_growth: getDefaultValue('property', 'property_inflation_rate', 3.0),
                rental_start_age: parseInt(members.age1 || '30') || 30,
                initial_debt_years: 28, // Default to 28 years as requested
                include_in_net_wealth: false, // Exclude all properties from net wealth
                new_property_value: null,
                sell_main_property: false,
                interest_only_period: false,
                lump_sum_payment_age: null,
                main_prop_sale_value: null,
                interest_only_end_age: null,
                allocate_to_investment: false,
                main_property_sale_age: null,
                interest_only_start_age: null,
                lump_sum_payment_amount: null,
                lump_sum_payment_source: null,
                additional_debt_repayments: null,
                sale_allocate_to_investment: false,
                additional_debt_repayments_end_age: null,
                additional_debt_repayments_start_age: null
              };
            })
          ];
        }

        // Define the known fields in the scenarios_data1 table
        const knownFields = [
          'id', 'scenario_name', 'household_id', 'name', 'starting_age', 'ending_age',
          'include_partner', 'partner_name', 'partner_starting_age', 'annual_income',
          'income_period', 'partner_annual_income', 'partner_income_period',
          'include_superannuation', 'additional_incomes', 'savings_amount', 'cash_reserve',
          'saving_percentage', 'annual_expenses1', 'expense_period1', 'annual_expenses2',
          'expense_period2', 'additional_expenses', 'second_expense', 'initial_investment',
          'annual_investment_contribution', 'annual_investment_return', 'inv_std_dev',
          'fund_type', 'fund_periods', 'investment_return_period', 'contribution_period',
          'inv_tax', 'initial_kiwisaver', 'kiwisaver_contribution', 'employer_contribution',
          'annual_kiwisaver_return', 'ks_std_dev', 'ks_periods', 'consolidate_kiwisaver',
          'partner_initial_kiwisaver', 'partner_kiwisaver_contribution',
          'partner_employer_contribution', 'partner_annual_kiwisaver_return',
          'partner_ks_std_dev', 'partner_ks_periods', 'partner_consolidate_kiwisaver',
          'property_value', 'debt', 'property_growth', 'debt_ir', 'initial_debt_years',
          'additional_debt_repayments', 'include_property_debt', 'show_repayments',
          'sell_main_property', 'main_property_sale_age', 'main_prop_sale_value',
          'sale_allocate_to_investment', 'downsize', 'downsize_age', 'new_property_value',
          'allocate_to_investment', 'pay_off_debt', 'additional_properties', 'inflation_rate',
          'simulations', 'confidence_interval', 'seed', 'household_name', 'created_at',
          'last_viewed_at', 'last_edited_at', 'annotations', 'sale_investment_fund',
          'fund_diversion', 'utilise_excess_cashflow', 'consolidate_kiwisaver_age',
          'partner_consolidate_kiwisaver_age', 'one_off_investments',
          'main_income_inflation_rate', 'partner_income_inflation_rate',
          'expense1_inflation_rate', 'expense2_inflation_rate', 'properties_data',
          'interest_only_period', 'interest_only_start_age', 'interest_only_end_age',
          'rental_income', 'rental_amount', 'rental_start_age', 'rental_end_age',
          'board_income', 'board_amount', 'board_start_age', 'board_end_age',
          'lump_sum_payment_age', 'lump_sum_payment_amount', 'lump_sum_payment_source',
          'user_id', 'org_id', 'investment_description', 'what_if',
          'investment_metrics', 'investment_funds_data', 'annual_investment_contributions'
        ];

        // Filter out any fields that don't exist in the scenarios_data1 table
        const filteredScenarioData: Record<string, any> = {};
        Object.keys(scenarioData).forEach(key => {
          if (knownFields.includes(key)) {
            filteredScenarioData[key] = (scenarioData as Record<string, any>)[key];
          } else {
          }
        });

        // Log the final scenario data with detailed property information

        // Log investment, savings, and KiwiSaver values

        // Log individual investment funds
        if (investmentFunds.length > 0) {
          // Ensure investment_metrics is properly formatted
          if (filteredScenarioData.investment_metrics) {
            filteredScenarioData.investment_metrics = typeof filteredScenarioData.investment_metrics === 'string'
              ? JSON.parse(filteredScenarioData.investment_metrics)
              : filteredScenarioData.investment_metrics;
          }
        }


        // Log all properties in properties_data

        // Log additional properties

        // Log the complete filtered scenario data for debugging

        // Log the final scenario data

        // 5. Insert the scenario into the database
        const { data: insertedScenario, error: insertError } = await supabase
          .from('scenarios_data1')
          .insert(filteredScenarioData)
          .select()
          .single();

        if (insertError) throw insertError;

        // Show success notification
        toast({
          title: "Scenario created successfully",
          description: "Opening planner page in a new tab...",
          variant: "default",
        });

        // 6. Open the planner page in a new tab
        window.open(`/protected/planner?scenarioId=${insertedScenario.id}&household_id=${selectedHousehold}`, '_blank');

        // 7. Refresh the scenarios list if onSuccess callback is provided
        if (onSuccess) {
          onSuccess();
        }

      } catch (error: any) {
        console.error('Error creating scenario automatically:', error);
        // Show error notification
        toast({
          title: "Error creating scenario",
          description: error.message || (error.details ? `${error.message}: ${error.details}` : "An unexpected error occurred"),
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
        onClose();
      }
    }
  };

  const navigateToHouseholdSetup = () => {
    router.push(`/protected/households/household/${selectedHousehold}/household_overview`);
    onClose();
  };

  const isFormValid = scenarioName.trim() !== '' && selectedHousehold !== '' && hasMembers && userId !== null && !isDuplicateName;

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Scenario</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="household">Household</Label>
            {preselectedHousehold ? (
              <Input
                id="household"
                value={preselectedHousehold.householdName}
                disabled
                className="bg-muted"
              />
            ) : (
              <div className="relative">
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className="w-full justify-between"
                  onClick={() => {
                    setOpen(!open);
                  }}
                >
                  {selectedHousehold
                    ? households.find((household) => household.id.toString() === selectedHousehold)?.householdName
                    : "Select household..."}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
                {open && (
                  <div className="absolute top-full z-10 w-full rounded-md border bg-popover shadow-md mt-1">
                    <div className="p-2">
                      <Input
                        placeholder="Search household..."
                        value={search}
                        onChange={(e) => {
                          setSearch(e.target.value);
                        }}
                        className="mb-2"
                      />
                      <div className="max-h-[300px] overflow-y-auto">
                        {isLoading ? (
                          <div className="p-2 text-center">Loading...</div>
                        ) : filteredHouseholds.length === 0 ? (
                          <div className="p-2 text-center">No households match your search</div>
                        ) : (
                          <div className="space-y-1">
                            {filteredHouseholds.map((household) => {
                              return (
                                <div
                                  key={household.id}
                                  className={cn(
                                    "flex items-center px-2 py-1.5 text-sm rounded-sm cursor-pointer hover:bg-accent hover:text-accent-foreground",
                                    selectedHousehold === household.id.toString() ? "bg-accent text-accent-foreground" : ""
                                  )}
                                  onClick={() => handleHouseholdChange(household.id.toString())}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      selectedHousehold === household.id.toString() ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                  {household.householdName}
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {selectedHousehold && !isLoading && (
            hasMembers ? (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Scenario Name
                </Label>
                <div className="col-span-3 space-y-1">
                  <Input
                    id="name"
                    value={scenarioName}
                    onChange={(e) => {
                      setScenarioName(e.target.value);
                      checkDuplicateScenarioName(e.target.value);
                    }}
                    className={cn(
                      isDuplicateName ? "border-red-500 focus-visible:ring-red-500" : ""
                    )}
                    required
                  />
                  {isDuplicateName && (
                    <p className="text-sm text-red-500 flex items-center gap-1 mt-1">
                      <AlertCircle size={14} />
                      A scenario with this name already exists. Please choose a different name.
                    </p>
                  )}
                </div>
              </div>
            ) : (
              <div className="border border-red-300 bg-red-50 p-4 rounded-md flex flex-col items-center gap-3">
                <div className="flex items-center gap-2 text-red-600">
                  <AlertCircle size={18} />
                  <p className="font-medium">Unable to create scenario for {selectedHouseholdName} due to insufficient data.</p>
                </div>
                <Button
                  variant="outline"
                  className="border-red-300 hover:bg-red-100 text-red-600"
                  onClick={navigateToHouseholdSetup}
                >
                  Click here to complete household setup
                </Button>
              </div>
            )
          )}
        </div>
        <DialogFooter>
          <Button
            onClick={handleCreateFromData}
            disabled={!isFormValid || isLoading}
          >
            Create Scenario Automatically
          </Button>
          <Button
            onClick={handleCreateUsingBuilder}
            disabled={!isFormValid || isLoading}
          >
            Open in Scenario Builder
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
