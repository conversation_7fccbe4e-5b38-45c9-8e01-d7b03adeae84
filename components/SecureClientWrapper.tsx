/**
 * Secure Client Wrapper Component
 *
 * Provides enhanced client-side authentication protection with server validation.
 * This component adds multiple layers of security to prevent authentication bypass.
 */

'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useFastAuth } from '@/hooks/useFastAuth';

import { Loader2 } from 'lucide-react';

interface SecureClientWrapperProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireMFA?: boolean;
  allowedRoles?: string[];
  redirectTo?: string;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
}

/**
 * Enhanced client wrapper with multiple security layers
 */
export default function SecureClientWrapper({
  children,
  requireAuth = true,
  requireMFA = false,
  allowedRoles,
  redirectTo = '/sign-in',
  loadingComponent,
  errorComponent
}: SecureClientWrapperProps) {
  const router = useRouter();
  const [isValidating, setIsValidating] = useState(true);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [lastValidation, setLastValidation] = useState<number>(0);

  const {
    user,
    isLoading,
    isAuthenticated,
    sessionState,
    error: authError,
    refresh,
    isInitialized
  } = useFastAuth({
    requireAuth,
    redirectTo,
    enableAutoRefresh: true,
    onAuthChange: (user: any) => {
      if (!user && requireAuth) {
        router.push(redirectTo);
      }
    }
  });

  // Enhanced validation with server-side verification
  const performSecurityValidation = useCallback(async () => {
    if (!requireAuth) {
      setIsValidating(false);
      return;
    }

    try {
      setValidationError(null);

      // Rate limit validation checks (every 30 seconds)
      const now = Date.now();
      if (now - lastValidation < 30000 && isAuthenticated) {
        setIsValidating(false);
        return;
      }

      // Skip additional validation if useFastAuth is already handling it
      // The enhanced session manager is already being used by useFastAuth
      setLastValidation(now);

      // Check role requirements
      if (allowedRoles && allowedRoles.length > 0 && user) {
        const response = await fetch('/api/auth/validate-role', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ allowedRoles })
        });

        if (!response.ok) {
          setValidationError('Role validation failed');
          router.push('/unauthorized');
          return;
        }
      }

      // Check MFA requirements
      if (requireMFA && user) {
        const response = await fetch('/api/auth/validate-mfa', {
          method: 'GET'
        });

        if (!response.ok) {
          const data = await response.json();
          if (data.needsChallenge) {
            router.push('/mfa-challenge');
            return;
          }
          setValidationError('MFA validation failed');
          router.push('/mfa-challenge');
          return;
        }
      }

      setIsValidating(false);
    } catch (error) {
      setValidationError('Security validation failed');
      if (requireAuth) {
        router.push(redirectTo);
      } else {
        setIsValidating(false);
      }
    }
  }, [requireAuth, isAuthenticated, user, allowedRoles, requireMFA, router, redirectTo, lastValidation]);

  // Initial validation
  useEffect(() => {
    if (!isLoading && (isAuthenticated || !requireAuth)) {
      performSecurityValidation();
    }
  }, [isLoading, isAuthenticated, requireAuth, performSecurityValidation]);

  // Periodic re-validation
  useEffect(() => {
    if (!requireAuth) return;

    const interval = setInterval(() => {
      if (isAuthenticated) {
        performSecurityValidation();
      }
    }, 60000); // Re-validate every minute

    return () => clearInterval(interval);
  }, [requireAuth, isAuthenticated, performSecurityValidation]);

  // Handle authentication errors
  useEffect(() => {
    if (authError && requireAuth) {
      setValidationError(authError);
      router.push(redirectTo);
    }
  }, [authError, requireAuth, router, redirectTo]);

  // Show loading state only if not initialized and authentication is required
  if (requireAuth && isLoading && !isInitialized) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-sm text-muted-foreground">Validating authentication...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (validationError || authError) {
    if (errorComponent) {
      return <>{errorComponent}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold text-red-600">Authentication Error</h2>
          <p className="text-sm text-muted-foreground mt-2">
            {validationError || authError}
          </p>
          <button
            onClick={() => {
              setValidationError(null);
              refresh();
            }}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return null; // Router will handle redirect
  }

  // Render protected content
  return <>{children}</>;
}

/**
 * Higher-order component for protecting client components
 */
export function withSecureClient<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<SecureClientWrapperProps, 'children'> = {}
) {
  return function ProtectedComponent(props: P) {
    return (
      <SecureClientWrapper {...options}>
        <Component {...props} />
      </SecureClientWrapper>
    );
  };
}

/**
 * Secure wrapper for admin-only client components
 */
export function SecureAdminWrapper({ children }: { children: React.ReactNode }) {
  return (
    <SecureClientWrapper
      requireAuth={true}
      allowedRoles={['admin', 'owner']}
      redirectTo="/unauthorized"
    >
      {children}
    </SecureClientWrapper>
  );
}

/**
 * Secure wrapper for MFA-protected client components
 */
export function SecureMFAWrapper({ children }: { children: React.ReactNode }) {
  return (
    <SecureClientWrapper
      requireAuth={true}
      requireMFA={true}
      redirectTo="/mfa-challenge"
    >
      {children}
    </SecureClientWrapper>
  );
}
