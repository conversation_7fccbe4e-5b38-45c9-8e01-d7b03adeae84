"use client";

import { Suspense } from "react"; // Import Suspense
import { signInAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { useSearchParams, useRouter } from "next/navigation";
import { useEffect } from "react";
import { toast } from "sonner";
import { clearSessionCache } from "@/utils/enhanced-session-manager";
import { createClient } from "@/utils/supabase/client";

// Component containing the original logic + useSearchParams hook
function SignInForm({ searchParams }: { searchParams: Message }) {
  const params = useSearchParams();
  const router = useRouter();


  useEffect(() => {
    // Clear any cached session data when loading sign-in page
    clearSessionCache();
  }, []);

  useEffect(() => {
    // Check for error message in URL and show toast
    const status = params.get('status');
    const message = params.get('message');

    if (status === 'error' && message) {
      // Show error toast for authentication errors
      if (message.includes('Invalid login credentials') ||
          message.includes('Email not confirmed') ||
          message.includes('Invalid email or password')) {
        toast.error('No user with that email or incorrect password', {
          description: 'Please check your credentials and try again',
          position: 'top-center',
          duration: 5000,
        });
      } else {
        // Show the original error for other types of errors
        toast.error('Authentication Error', {
          description: message,
          position: 'top-center',
          duration: 5000,
        });
      }
    }

    // Check for success message
    if (status === 'success' && message) {
      toast.success('Success', {
        description: message,
        position: 'top-center',
        duration: 5000,
      });
    }
  }, [params]);

  // Monitor authentication state changes
  useEffect(() => {
    const supabase = createClient();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Sign-in page: Auth state change detected:', event, !!session);

        if (event === 'SIGNED_IN' && session) {
          console.log('Sign-in page: User signed in successfully, redirecting...');

          // Clear session cache to force fresh validation
          clearSessionCache();

          // Show success toast
          toast.success('Sign in successful!', {
            description: 'Redirecting to your dashboard...',
            position: 'top-center',
            duration: 2000,
          });

          // Small delay to ensure session is properly established
          setTimeout(() => {
            router.push('/protected');
          }, 500);
        } else if (event === 'SIGNED_OUT') {
          console.log('Sign-in page: User signed out');
          clearSessionCache();
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [router]);

  return (
    <div className="flex min-w-full justify-center items-center min-h-screen bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full mx-4">
        <form className="flex flex-col">
          <h1 className="text-2xl font-medium">Sign in</h1>
          <p className="text-sm text-gray-600">
            Don't have an account?{" "}
            <Link className="text-blue-600 font-medium underline" href="/sign-up">
              Sign up
            </Link>
          </p>
          <div className="flex flex-col gap-4 mt-8">
            <Label htmlFor="email">Email</Label>
            <Input name="email" placeholder="<EMAIL>" required />
            <div className="flex justify-between items-center">
              <Label htmlFor="password">Password</Label>
              <Link
                className="text-xs text-blue-600 underline"
                href="/forgot-password"
              >
                Forgot Password?
              </Link>
            </div>
            <Input
              type="password"
              name="password"
              placeholder="Your password"
              required
            />
            <SubmitButton pendingText="Signing In..." formAction={signInAction}>
              Sign in
            </SubmitButton>
            <FormMessage message={searchParams} />
          </div>
        </form>
      </div>
    </div>
  );
}

// Default export page component wraps the form in Suspense
export default function Login({ searchParams }: { searchParams: Message }) {
  return (
    // Wrap the component using useSearchParams in Suspense
    <Suspense fallback={<div>Loading...</div>}>
      <SignInForm searchParams={searchParams} />
    </Suspense>
  );
}
