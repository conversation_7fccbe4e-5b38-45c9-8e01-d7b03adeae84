'use client';
import { useState } from 'react';
import { usePathname } from 'next/navigation';
import PageHeader from "@/components/PageHeader";
import Sidebar from "@/components/sidebars/Sidebar";
import HouseholdSidebar from "@/components/sidebars/HouseholdsSidebar";
import DeviceRegistration from "@/components/DeviceRegistration";
import AdminSidebar from '@/components/sidebars/AdminSidebar';
import SecureClientWrapper from '@/components/SecureClientWrapper';
import SessionStatusIndicator from '@/components/SessionStatusIndicator';

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();
  const isPlanner = pathname.includes('/planner');
  const isPresentation = pathname.includes('/presentation');
  const isHousehold = pathname.includes('/households/household');
  const isAdmin = pathname.includes('/admin');
  const isWorkflowDetail = pathname.match(/\/workflows\/[^/]+$/) !== null;

  if (isPlanner || isPresentation) {
    return (
      <SecureClientWrapper requireAuth={true}>
        <div className="h-screen">
          {/* Device registration component Disabled for now
          <DeviceRegistration />
          */}
          <main className="h-full overflow-auto">
            {children}
          </main>
          <SessionStatusIndicator position="top-right" />
        </div>
      </SecureClientWrapper>
    );
  }
  if (isHousehold) {
    // Extract only the household ID part from the pathname
    const householdIdSegment = pathname.split('/households/household/')[1];
    const actualHouseholdId = householdIdSegment ? householdIdSegment.split('/')[0] : '';

    return (
      <SecureClientWrapper requireAuth={true}>
        <div className="flex flex-col h-screen">
          <PageHeader isCollapsed={isCollapsed} setIsCollapsed={setIsCollapsed} />
          <div className="flex flex-1 overflow-hidden">
            <HouseholdSidebar
              householdId={actualHouseholdId}
              isCollapsed={isCollapsed}
              setIsCollapsed={setIsCollapsed}
            />
            <main className="flex-1 overflow-auto">
              {children}
            </main>
          </div>
          <SessionStatusIndicator position="top-right" />
        </div>
      </SecureClientWrapper>
    );
  }
  return (
    <SecureClientWrapper
      requireAuth={true}
      allowedRoles={isAdmin ? ['admin', 'owner'] : undefined}
      redirectTo={isAdmin ? '/unauthorized' : '/sign-in'}
    >
      <div className="flex flex-col h-screen">
        <PageHeader isCollapsed={isCollapsed} setIsCollapsed={setIsCollapsed} />
        <div className="flex flex-1 overflow-hidden">
          {isHousehold ? (
            <HouseholdSidebar
              householdId={pathname.split('/')[4]}
              isCollapsed={isCollapsed}
              setIsCollapsed={setIsCollapsed}
            />
          ) : isAdmin ? (
            <AdminSidebar
              isCollapsed={isCollapsed}
              setIsCollapsed={setIsCollapsed}
            />
          ) : (
            <Sidebar
              isCollapsed={isCollapsed}
              setIsCollapsed={setIsCollapsed}
            />
          )}
          <main className="flex-1 overflow-auto">
            {children}
          </main>
        </div>
        <SessionStatusIndicator position="top-right" />
      </div>
    </SecureClientWrapper>
  );
}
