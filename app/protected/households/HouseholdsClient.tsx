/**
 * Client Component for Households
 *
 * This component receives server-validated data and handles client-side interactions.
 * It works with the server-side protected page to provide secure data access.
 */

"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import CreateHouseholdModal from '@/components/modals/CreateHouseholdModal';
import { createClient } from '@/utils/supabase/client';
import { HouseholdsTable } from '@/components/tables/HouseholdsTable';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

interface Household {
  id: number;
  householdName: string;
  members: string;
  user_id: string;
  org_id: string | null;
  created_at: string;
  updated_at: string;
}

interface UserProfile {
  user_id: string;
  org_id: string | null;
  org_role: string | null;
  name: string | null;
}

interface HouseholdsClientProps {
  initialHouseholds: Household[];
  userProfile: UserProfile;
  user: any;
}

export default function HouseholdsClient({
  initialHouseholds,
  userProfile,
  user
}: HouseholdsClientProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [households, setHouseholds] = useState<Household[]>(initialHouseholds);
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'user' | 'organization'>('user');
  const supabase = createClient();
  const router = useRouter();

  useEffect(() => {
    // Initialize view mode from localStorage
    const savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode === 'user' || savedViewMode === 'organization') {
      setViewMode(savedViewMode as 'user' | 'organization');
    } else {
      setViewMode('user');
      localStorage.setItem('viewMode', 'user');
    }

    const handleViewModeChange = () => {
      const newViewMode = localStorage.getItem('viewMode');
      if (newViewMode === 'user' || newViewMode === 'organization') {
        setViewMode(newViewMode as 'user' | 'organization');
      }
    };

    window.addEventListener('viewModeChange', handleViewModeChange);
    return () => window.removeEventListener('viewModeChange', handleViewModeChange);
  }, []);

  useEffect(() => {
    if (userProfile.user_id) {
      fetchHouseholds();
    }
  }, [viewMode, userProfile]);

  const fetchHouseholds = async () => {
    setIsLoading(true);
    
    try {
      let query = supabase.from('households').select('*');

      // Apply view mode filter with proper authorization
      if (viewMode === 'user') {
        query = query.eq('user_id', user.id);
      } else if (viewMode === 'organization' && userProfile.org_id) {
        query = query.eq('org_id', userProfile.org_id);
      }

      query = query.order('created_at', { ascending: false });

      const { data, error } = await query;

      if (error) {
        setHouseholds([]);
      } else {
        setHouseholds(data || []);
      }
    } catch (error) {
      setHouseholds([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    fetchHouseholds();
  };

  const handleCreateHousehold = () => {
    setIsModalOpen(true);
  };

  const handleEditClick = (householdId: number) => {
    router.push(`/protected/households/household/${householdId}`);
  };

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <div className="flex-1 min-h-0">
          <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <HouseholdsTable
                  data={households}
                  onDataChange={fetchHouseholds}
                  onCreateHousehold={handleCreateHousehold}
                  onEditClick={handleEditClick}
                  viewMode={viewMode}
                  onViewModeChange={(value) => {
                    setViewMode(value);
                    localStorage.setItem('viewMode', value);
                    window.dispatchEvent(new Event('viewModeChange'));
                  }}
                />
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      <CreateHouseholdModal isOpen={isModalOpen} onClose={handleModalClose} />
    </div>
  );
}
