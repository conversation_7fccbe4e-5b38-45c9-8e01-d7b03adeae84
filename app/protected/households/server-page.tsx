/**
 * Server-Side Protected Households Page
 *
 * This is an example of how to implement server-side authentication
 * that cannot be bypassed by client-side manipulation.
 */

import { requireAuth } from '@/utils/auth-guard';
import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import HouseholdsClient from './HouseholdsClient';

interface Household {
  id: number;
  householdName: string;
  members: string;
  user_id: string;
  org_id: string | null;
  created_at: string;
  updated_at: string;
}

interface UserProfile {
  user_id: string;
  org_id: string | null;
  org_role: string | null;
  name: string | null;
}

/**
 * Server component that validates authentication and fetches data
 * This cannot be bypassed by disabling JavaScript
 */
export default async function ServerHouseholdsPage() {
  // Server-side authentication check - cannot be bypassed
  const user = await requireAuth();
  
  if (!user) {
    redirect('/sign-in');
  }

  // Server-side data fetching with proper authorization
  const supabase = createClient();
  
  try {
    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('user_id, org_id, org_role, name')
      .eq('user_id', user.id)
      .single();

    if (profileError) {
      redirect('/sign-in');
    }

    // Fetch households based on user's access level
    let householdsQuery = supabase.from('households').select('*');
    
    // Default to user's own households
    householdsQuery = householdsQuery.eq('user_id', user.id);
    
    // If user has organization access, they can see org households too
    if (profile?.org_id) {
      householdsQuery = supabase
        .from('households')
        .select('*')
        .or(`user_id.eq.${user.id},org_id.eq.${profile.org_id}`);
    }

    const { data: households, error: householdsError } = await householdsQuery
      .order('created_at', { ascending: false });

    if (householdsError) {
      // If there's an error, still render the page but with empty data
      return (
        <HouseholdsClient
          initialHouseholds={[]}
          userProfile={profile as UserProfile}
          user={user}
        />
      );
    }

    // Pass server-fetched data to client component
    return (
      <HouseholdsClient
        initialHouseholds={households as Household[]}
        userProfile={profile as UserProfile}
        user={user}
      />
    );

  } catch (error) {
    // If any server-side operation fails, redirect to sign-in
    redirect('/sign-in');
  }
}

/**
 * Alternative: Higher-order component approach
 */
export async function withServerAuth<P extends object>(
  Component: React.ComponentType<P & { user: any }>
) {
  return async function ProtectedServerComponent(props: P) {
    const user = await requireAuth();
    
    if (!user) {
      redirect('/sign-in');
    }

    return <Component {...props} user={user} />;
  };
}

/**
 * Example of role-based server protection
 */
export async function AdminOnlyServerPage({ children }: { children: React.ReactNode }) {
  const user = await requireAuth();
  
  // Get user profile to check role
  const supabase = createClient();
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('org_role')
    .eq('user_id', user.id)
    .single();

  if (error || !profile || !['admin', 'owner'].includes(profile.org_role)) {
    redirect('/unauthorized');
  }

  return <>{children}</>;
}

/**
 * Example of MFA-protected server page
 */
export async function MFAProtectedServerPage({ children }: { children: React.ReactNode }) {
  // This will check both authentication and MFA
  const { requireMFA } = await import('@/utils/auth-guard');
  const user = await requireMFA();
  
  if (!user) {
    redirect('/mfa-challenge');
  }

  return <>{children}</>;
}
