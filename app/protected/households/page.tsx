"use client";

import { useState, useEffect } from 'react';
import { Card, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Sidebar from '@/components/sidebars/Sidebar';
import CreateHouseholdModal from '@/components/modals/CreateHouseholdModal';
import { createClient } from '@/utils/supabase/client';
import { HouseholdsTable } from '@/components/tables/HouseholdsTable';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

interface Household {
  id: number;
  householdName: string;
  members: string;
}

export default function Households() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [households, setHouseholds] = useState<Household[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'user' | 'organization'>('user');
  const [profileData, setProfileData] = useState<{user_id?: string, org_id?: string}>({});
  const supabase = createClient();
  const router = useRouter();

  useEffect(() => {
    // Ensure viewMode has a default value if not in localStorage
    const savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode === 'user' || savedViewMode === 'organization') {
      setViewMode(savedViewMode as 'user' | 'organization');
    } else {
      // Set default to 'user' if nothing is stored
      setViewMode('user');
      localStorage.setItem('viewMode', 'user');
    }

    const handleViewModeChange = () => {
      const newViewMode = localStorage.getItem('viewMode');
      if (newViewMode === 'user' || newViewMode === 'organization') {
        setViewMode(newViewMode as 'user' | 'organization');
      }
    };

    window.addEventListener('viewModeChange', handleViewModeChange);
    return () => window.removeEventListener('viewModeChange', handleViewModeChange);
  }, []);

  useEffect(() => {
    const fetchUserProfile = async () => {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (user) {
        const { data, error } = await supabase
          .from('profiles')
          .select('user_id, org_id')
          .eq('user_id', user.id)
          .single();

        if (data) {
          setProfileData({
            user_id: data.user_id,
            org_id: data.org_id
          });
        }
      }
    };

    fetchUserProfile();
  }, []);

  useEffect(() => {
    if (profileData.user_id) {
      fetchHouseholds();
    }
  }, [viewMode, profileData]);

  const fetchHouseholds = async () => {
    setIsLoading(true);
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      setIsLoading(false);
      return;
    }

    let query = supabase.from('households').select('*');

    // Apply view mode filter
    if (viewMode === 'user') {
      query = query.eq('user_id', user.id);
    } else if (viewMode === 'organization' && profileData.org_id) {
      query = query.eq('org_id', profileData.org_id);
    }

    query = query.order('created_at', { ascending: false });

    const { data, error } = await query;

    if (error) {
      setHouseholds([]);
    } else {
      setHouseholds(data || []);
    }
    setIsLoading(false);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    fetchHouseholds();
  };

  const handleCreateHousehold = () => {
    setIsModalOpen(true);
  };

  const handleEditClick = (householdId: number) => {
    router.push(`/protected/households/household/${householdId}`);
  };

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col min-h-0 pt-2 pb-2 pr-2 pl-0 overflow-hidden">
        <div className="flex-1 min-h-0">
          <Card className="h-[calc(100vh-62px)] mt-[50px] flex flex-col">
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <HouseholdsTable
                  data={households}
                  onDataChange={fetchHouseholds}
                  onCreateHousehold={handleCreateHousehold}
                  onEditClick={handleEditClick}
                  viewMode={viewMode}
                  onViewModeChange={(value) => {
                    setViewMode(value);
                    localStorage.setItem('viewMode', value);
                    window.dispatchEvent(new Event('viewModeChange'));
                  }}
                />
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      <CreateHouseholdModal isOpen={isModalOpen} onClose={handleModalClose} />
    </div>
  );
}
