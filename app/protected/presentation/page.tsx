'use client';

import { useEffect, useState, Suspense, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import HouseholdSelector from '@/components/HouseholdSelector';
import ScenarioSelector from '@/components/ScenarioSelector';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PersonalTab } from '@/components/tabs/PersonalTab';
import { IncomeTab } from '@/components/tabs/IncomeTab';
import { ExpenseTab } from '@/components/tabs/ExpenseTab';
import { SavingsTab } from '@/components/tabs/SavingsTab';
import { InvestmentTab } from '@/components/tabs/InvestmentTab';
import { KiwiSaverTab } from '@/components/tabs/KiwiSaverTab';
import { PropertyTab } from '@/components/tabs/PropertyTab';
import { MiscTab } from '@/components/tabs/MiscTab';
import { WhatIfTab } from '@/components/tabs/WhatIfTab';
import { Area, AreaChart, Bar, BarChart, CartesianGrid, XAxis, YAxis, Legend, ComposedChart, Line } from 'recharts';
import { useFinancialCalculations } from '@/app/utils/financialCalculations';
import { ChartConfig, ChartContainer } from '@/components/ui/chart';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { Maximize2, Download } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import ModellingTableWithTabs from '@/components/tables/ModellingTableWithTabs';
import MonteCarloResults from '@/components/MonteCarloResults';
import { useCurrentPng } from 'recharts-to-png';
import FileSaver from 'file-saver';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { FUND_TYPES } from '@/app/constants/fundTypes';
import { CheckedState } from '@radix-ui/react-checkbox';
import NetWealth from '@/components/graphs/NetWealth';
import PresentationNetWealth from '@/components/graphs/PresentationNetWealth';
import KeyMetrics from '@/components/graphs/KeyMetrics';
import { Property } from '@/components/graphs/Property';
import { Cashflow } from '@/components/graphs/Cashflow';
import { FinancialData } from '@/app/utils/financialTypes';
import { InputData, chartConfig } from './types';
import { ExpenseBreakdown } from '@/components/graphs/ExpenseBreakdown';
import { IncomeBreakdown } from '@/components/graphs/IncomeBreakdown';
import ResizableCardContainer from '@/components/ResizableCardContainer';
import PresentationSidebar from '@/components/sidebars/PresentationSidebar';
import { debounce } from 'lodash';
import PresentationModal from '@/components/modals/PresentationModal';

export default function Presentation() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PresentationContent />
    </Suspense>
  );
}

function PresentationContent() {
  const searchParams = useSearchParams();
  const scenarioId1 = searchParams.get('scenarioId1');
  const scenarioId2 = searchParams.get('scenarioId2');
  const scenarioId3 = searchParams.get('scenarioId3');
  const householdId = searchParams.get('household_id');

  const [isLoading, setIsLoading] = useState(true);
  const [inputData, setInputData] = useState<InputData | null>(null);
  const [selectedScenario, setSelectedScenario] = useState<any | null>(null);
  const [allMetrics, setAllMetrics] = useState<any[]>([]);
  const [hasPartner, setHasPartner] = useState(false);
  const [chanceOfSuccess, setChanceOfSuccess] = useState<number | null>(null);
  const [successfulScenarios, setSuccessfulScenarios] = useState<number | null>(null);
  const [failedScenarios, setFailedScenarios] = useState<number | null>(null);
  const [minNetWealthAtAge, setMinNetWealthAtAge] = useState<number[]>([]);
  const [maxNetWealthAtAge, setMaxNetWealthAtAge] = useState<number[]>([]);
  const [averageNetWealthAtAge, setAverageNetWealthAtAge] = useState<number[]>([]);
  const { calculateFinancialLife } = useFinancialCalculations(inputData);
  const [householdName, setHouseholdName] = useState<string>('');
  const [mainName, setMainName] = useState<string>('');
  const [partnerName, setPartnerName] = useState<string>('');
  const [selectedAge, setSelectedAge] = useState<number | null>(null);
  // State to track when data is ready for animations
  const [dataReady, setDataReady] = useState(false);
  const [chartVisible, setChartVisible] = useState(true);

  // State for presentation mode
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isOverlayMode, setIsOverlayMode] = useState(false);
  const [isPresentationModalOpen, setIsPresentationModalOpen] = useState(false);
  const [availableScenarios, setAvailableScenarios] = useState<Array<{
    id: number;
    scenario_name: string;
    household_id: number;
    household_name: string;
  }>>([]);
  const [activeScenarioId, setActiveScenarioId] = useState<number | null>(null);

  // This array stores the scenarios with their metrics for overlay mode
  const [scenarioData, setScenarioData] = useState<Array<{
    id: number;
    scenario_name: string;
    household_id: number;
    household_name: string;
    allMetrics: any[];
    ending_age?: number;
  }>>([]);

  // Store scenario-specific input data to persist changes across scenario switches
  const [scenarioInputData, setScenarioInputData] = useState<Record<number, InputData>>({});

  const updateInputData = (updater: (prevData: NonNullable<InputData>) => NonNullable<InputData>) => {
    setInputData((prevData) => {
      if (prevData === null) return prevData;
      const updatedData = updater(prevData);

      // Schedule a debounced recalculation
      debouncedRecalculate(updatedData);

      // Save the updated data to scenario-specific state if we have an active scenario
      if (activeScenarioId) {
        setScenarioInputData(prev => ({
          ...prev,
          [activeScenarioId]: updatedData
        }));
      }

      return updatedData;
    });
  };

  // Load scenarios on initial render
  useEffect(() => {
    const loadScenarios = async () => {
      setIsLoading(true);
      // Set dataReady to false initially
      setDataReady(false);
      // Hide charts initially
      setChartVisible(false);

      if (!householdId) {
        setIsLoading(false);
        return;
      }

      // Collect all scenario IDs
      const scenarioIds = [scenarioId1, scenarioId2, scenarioId3]
        .filter(id => id !== null)
        .map(id => parseInt(id as string));

      if (scenarioIds.length === 0) {
        setIsLoading(false);
        return;
      }

      const supabase = createClient();

      try {
        // Fetch all scenarios for this household
        const { data: scenariosData, error } = await supabase
          .from('scenarios_data1')
          .select('id, scenario_name, household_id, household_name')
          .eq('household_id', householdId)
          .in('id', scenarioIds);

        if (error) throw error;

        if (scenariosData && scenariosData.length > 0) {
          setAvailableScenarios(scenariosData);

          // Set the first scenario as active
          const firstScenarioId = scenariosData[0].id;
          setActiveScenarioId(firstScenarioId);

          // Load the first scenario
          await fetchScenarioDetails(firstScenarioId);
        } else {
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Error loading scenarios:', error);
        setIsLoading(false);
      }
    };

    loadScenarios();
  }, [householdId, scenarioId1, scenarioId2, scenarioId3]);

  // Handle scenario switching
  const handleScenarioChange = async (scenarioId: number) => {
    if (scenarioId === activeScenarioId) return;

    // Hide chart during transition
    setChartVisible(false);

    // Set dataReady to false to prevent animations during data loading
    setDataReady(false);
    setIsLoading(true);

    // Save current scenario's input data before switching
    if (activeScenarioId && inputData) {
      // Save to scenarioInputData
      setScenarioInputData(prev => ({
        ...prev,
        [activeScenarioId]: inputData
      }));

      // If in overlay mode, also update the scenarioData with current metrics
      if (isOverlayMode) {
        setScenarioData(prev => {
          const existingIndex = prev.findIndex(s => s.id === activeScenarioId);
          if (existingIndex >= 0) {
            const updatedData = [...prev];
            updatedData[existingIndex] = {
              ...updatedData[existingIndex],
              allMetrics: allMetrics,
              ending_age: inputData.ending_age
            };
            return updatedData;
          }
          return prev;
        });
      }
    }

    // Update active scenario ID
    setActiveScenarioId(scenarioId);

    // Fetch scenario details (this will check for saved input data)
    await fetchScenarioDetails(scenarioId);

    // Update scenarioData for overlay mode
    if (isOverlayMode) {
      // In overlay mode, we want to update the active scenario's data
      // without affecting the other scenarios
      const activeScenario = availableScenarios.find(s => s.id === scenarioId);
      if (activeScenario && inputData) {
        setScenarioData(prev => {
          const existingIndex = prev.findIndex(s => s.id === scenarioId);
          if (existingIndex >= 0) {
            // Update existing scenario data
            const updatedData = [...prev];
            updatedData[existingIndex] = {
              ...updatedData[existingIndex],
              allMetrics: allMetrics,
              ending_age: inputData.ending_age
            };
            return updatedData;
          } else {
            // Add new scenario data
            return [...prev, {
              id: scenarioId,
              scenario_name: activeScenario.scenario_name,
              household_id: activeScenario.household_id,
              household_name: activeScenario.household_name,
              allMetrics: allMetrics,
              ending_age: inputData.ending_age
            }];
          }
        });
      }
    }

    // Set isLoading to false
    setIsLoading(false);

    // Increment chart key to force remount of chart components
    setChartKey(prevKey => prevKey + 1);

    // Wait for next render cycle before showing chart again
    setTimeout(() => {
      setChartVisible(true);

      // Set dataReady to true after a delay to ensure DOM has updated
      // This delay is crucial for the animation to restart properly
      setTimeout(() => {
        setDataReady(true);
      }, 100);
    }, 100);
  };

  // Handle removing a scenario from overlay mode
  const handleRemoveScenario = (scenarioId: number) => {
    setScenarioData(prev => prev.filter(s => s.id !== scenarioId));
  };

  // Update scenarioData array for overlay mode
  const updateScenarioData = () => {
    // If we have the current scenario's metrics, add them to the scenarioData array
    if (activeScenarioId && allMetrics.length > 0) {
      // Find the active scenario in availableScenarios
      const activeScenario = availableScenarios.find(s => s.id === activeScenarioId);

      if (activeScenario && inputData) {
        // Create a deep copy of the metrics to ensure each scenario has its own copy
        const metricsCopy = JSON.parse(JSON.stringify(allMetrics));

        // Get the ending age from the input data
        const endingAge = inputData.ending_age;

        // Add to scenarioData array if not already there, or update if it exists
        setScenarioData(prev => {
          const existingIndex = prev.findIndex(s => s.id === activeScenarioId);

          if (existingIndex >= 0) {
            // Update existing scenario data
            const updatedData = [...prev];
            updatedData[existingIndex] = {
              ...updatedData[existingIndex],
              allMetrics: metricsCopy,
              ending_age: endingAge
            };
            return updatedData;
          } else {
            // Add new scenario data
            return [...prev, {
              id: activeScenarioId,
              scenario_name: activeScenario.scenario_name,
              household_id: activeScenario.household_id,
              household_name: activeScenario.household_name,
              allMetrics: metricsCopy,
              ending_age: endingAge
            }];
          }
        });

        // Also ensure the current input data is saved to scenarioInputData
        setScenarioInputData(prev => ({
          ...prev,
          [activeScenarioId]: inputData
        }));
      }
    }
  };

  // Effect to update scenarioData when overlay mode is toggled or when metrics change
  useEffect(() => {
    if (isOverlayMode) {
      if (activeScenarioId && allMetrics.length > 0) {
        // Add the current scenario to the scenarioData array if not already there
        updateScenarioData();
      }

      // Load all available scenarios when overlay mode is enabled
      const loadAllScenarios = async () => {
        // Skip if we don't have any available scenarios
        if (availableScenarios.length === 0) return;

        // For each available scenario that isn't already in scenarioData, load its data
        for (const scenario of availableScenarios) {
          // Skip the active scenario as it's already handled above
          if (scenario.id === activeScenarioId) continue;

          // Check if this scenario is already in scenarioData
          const existingScenario = scenarioData.find(s => s.id === scenario.id);
          if (!existingScenario) {
            // If we have saved input data for this scenario, use it to calculate metrics
            if (scenarioInputData[scenario.id]) {
              const savedData = scenarioInputData[scenario.id];
              const result = calculateFinancialLife(savedData as unknown as FinancialData);

              // Add this scenario to scenarioData
              setScenarioData(prev => [
                ...prev,
                {
                  id: scenario.id,
                  scenario_name: scenario.scenario_name,
                  household_id: scenario.household_id,
                  household_name: scenario.household_name,
                  allMetrics: result.allMetrics,
                  ending_age: savedData.ending_age
                }
              ]);
            } else {
              // Otherwise, fetch the scenario data from the database
              // This is a simplified version - in a real implementation, you'd want to
              // fetch the data and calculate metrics for each scenario
              try {
                const supabase = createClient();
                const { data: scenarioData } = await supabase
                  .from('scenarios_data1')
                  .select('*')
                  .eq('id', scenario.id)
                  .single();

                if (scenarioData) {
                  // Process the scenario data to get input data
                  const inputData = processScenarioData(scenarioData);

                  // Calculate metrics
                  const result = calculateFinancialLife(inputData as unknown as FinancialData);

                  // Add this scenario to scenarioData
                  setScenarioData(prev => [
                    ...prev,
                    {
                      id: scenario.id,
                      scenario_name: scenario.scenario_name,
                      household_id: scenario.household_id,
                      household_name: scenario.household_name,
                      allMetrics: result.allMetrics,
                      ending_age: inputData.ending_age
                    }
                  ]);

                  // Save the input data for future use
                  setScenarioInputData(prev => ({
                    ...prev,
                    [scenario.id]: inputData
                  }));
                }
              } catch (error) {
                console.error(`Error loading scenario ${scenario.id}:`, error);
              }
            }
          }
        }
      };

      loadAllScenarios();
    } else if (!isOverlayMode) {
      // Clear scenarioData when exiting overlay mode
      setScenarioData([]);
    }
  }, [isOverlayMode, activeScenarioId, allMetrics, availableScenarios]);

  // Initialize investment funds when inputData is loaded
  useEffect(() => {
    if (inputData) {
      // Initialize investment funds if needed
      const initializeInvestmentFunds = () => {
        // Check if we have a legacy initial_investment but no individual investment funds
        if (inputData.initial_investment > 0 &&
            !(inputData.initial_investment1 || inputData.initial_investment2 ||
              inputData.initial_investment3 || inputData.initial_investment4 ||
              inputData.initial_investment5)) {

          // Distribute the total investment across all 5 funds
          const totalInvestment = inputData.initial_investment;

          setInputData((prevData) => {
            if (!prevData) return prevData;

            return {
              ...prevData,
              initial_investment1: totalInvestment * 0.2,
              annual_investment_return1: prevData.annual_investment_return || 5.5,
              inv_std_dev1: prevData.inv_std_dev || 8.0,
              investment_description1: prevData.investment_description || 'Investment Fund 1',

              initial_investment2: totalInvestment * 0.2,
              annual_investment_return2: 5.5,
              inv_std_dev2: 8.0,
              investment_description2: 'Investment Fund 2',

              initial_investment3: totalInvestment * 0.2,
              annual_investment_return3: 5.5,
              inv_std_dev3: 8.0,
              investment_description3: 'Investment Fund 3',

              initial_investment4: totalInvestment * 0.2,
              annual_investment_return4: 5.5,
              inv_std_dev4: 8.0,
              investment_description4: 'Investment Fund 4',

              initial_investment5: totalInvestment * 0.2,
              annual_investment_return5: 5.5,
              inv_std_dev5: 8.0,
              investment_description5: 'Investment Fund 5',

              // Clear the legacy initial_investment field to avoid confusion
              initial_investment: 0,

              // Set withdrawal priorities for all 5 funds
              withdrawal_priorities: [1, 2, 3, 4, 5]
            };
          });
        }

        // If no active funds in priority list, default to fund 1
        if (!inputData.withdrawal_priorities || inputData.withdrawal_priorities.length === 0) {
          setInputData((prevData) => {
            if (!prevData) return prevData;

            return {
              ...prevData,
              withdrawal_priorities: [1]
            };
          });
        }
      };

      // Initialize investment funds
      initializeInvestmentFunds();

      // Only calculate on initial load or when loading a scenario
      // All other updates will use the debounced calculation
      if (isLoading) {
        const result = calculateFinancialLife(inputData as unknown as FinancialData);
        setAllMetrics(result.allMetrics);
        setChanceOfSuccess(result.chanceOfSuccess);
        setSuccessfulScenarios(result.successfulScenarios);
        setFailedScenarios(result.failedScenarios);
        setMinNetWealthAtAge(result.worstNetWealthAtAge);
        setMaxNetWealthAtAge(result.bestNetWealthAtAge);
        setAverageNetWealthAtAge(result.averageNetWealthAtAge);

        // Set loading to false after initial calculation
        setIsLoading(false);

        // Hide chart during transition
        setChartVisible(false);

        // Increment chart key to force remount of chart components
        setChartKey(prevKey => prevKey + 1);

        // Wait for next render cycle before showing chart again
        setTimeout(() => {
          setChartVisible(true);

          // Set dataReady to true after a delay to ensure DOM has updated
          // This delay is crucial for the animation to restart properly
          setTimeout(() => {
            setDataReady(true);
          }, 100);
        }, 100);
      }
    }
  }, [inputData, isLoading]);

  const fetchHouseholdName = async (householdId: number) => {
    const supabase = createClient();
    const { data: householdData, error } = await supabase
      .from('households')
      .select('householdName, members')
      .eq('id', householdId)
      .single();

    if (error) {
      console.error('Error fetching household name:', error);
      return;
    }
    if (householdData) {
      setHouseholdName(householdData.householdName);

      // Extract member names from the members JSON field
      if (householdData.members) {
        setMainName(householdData.members.name1 || 'Main');
        setPartnerName(householdData.members.name2 || 'Partner');
      }
    }
  };

  // Then update the fetchScenarioDetails function
  const fetchScenarioDetails = async (id: number) => {
    setIsLoading(true);

    // Check if we have saved input data for this scenario
    if (scenarioInputData[id]) {
      // Use the saved input data
      setInputData(scenarioInputData[id]);
      setSelectedScenario({
        id,
        scenario_name: scenarioInputData[id].name,
        household_id: scenarioInputData[id].household_id,
        household_name: scenarioInputData[id].household_name
      });
      setHasPartner(scenarioInputData[id].includePartner);

      // Calculate financial metrics based on saved input data
      const result = calculateFinancialLife(scenarioInputData[id] as unknown as FinancialData);
      setAllMetrics(result.allMetrics);
      setChanceOfSuccess(result.chanceOfSuccess);
      setSuccessfulScenarios(result.successfulScenarios);
      setFailedScenarios(result.failedScenarios);
      setMinNetWealthAtAge(result.worstNetWealthAtAge);
      setMaxNetWealthAtAge(result.bestNetWealthAtAge);
      setAverageNetWealthAtAge(result.averageNetWealthAtAge);

      // Fetch household name for UI display
      await fetchHouseholdName(scenarioInputData[id].household_id);

      // Set annotations if they exist in the saved data
      if (scenarioInputData[id].annotations) {
        setAnnotations({ netWealth: scenarioInputData[id].annotations });
      }

      setIsLoading(false);
      return;
    }

    // If no saved data, fetch from database
    const supabase = createClient();
    const { data: scenarioData } = await supabase
      .from('scenarios_data1')
      .select('*')
      .eq('id', id)
      .single();

    if (scenarioData) {
      await fetchHouseholdName(scenarioData.household_id);

      // Set annotations if they exist
      if (scenarioData.annotations) {
        setAnnotations({ netWealth: scenarioData.annotations });
      }

      // Type guard to ensure fund type is valid
      const getFundType = (type: string | null): keyof typeof FUND_TYPES => {
        if (type && type in FUND_TYPES) {
          return type as keyof typeof FUND_TYPES;
        }
        return 'Balanced'; // default value
      };

      // Parse arrays from JSON if they exist
      const income_period = Array.isArray(scenarioData.income_period) ?
        scenarioData.income_period : [scenarioData.starting_age || 0, 64];
      const partner_income_period = Array.isArray(scenarioData.partner_income_period) ?
        scenarioData.partner_income_period : [scenarioData.partner_starting_age || 0, 64];
      const expense_period1 = Array.isArray(scenarioData.expense_period1) ?
        scenarioData.expense_period1 : [scenarioData.starting_age || 0, 64];
      const expense_period2 = Array.isArray(scenarioData.expense_period2) ?
        scenarioData.expense_period2 : [65, scenarioData.ending_age || 100];
      const investment_return_period = Array.isArray(scenarioData.investment_return_period) ?
        scenarioData.investment_return_period : [scenarioData.starting_age || 0, scenarioData.ending_age || 100];
      const contribution_period = Array.isArray(scenarioData.contribution_period) ?
        scenarioData.contribution_period : [scenarioData.starting_age || 0, 64];

      // Define property data type
      interface PropertyData {
        property_title: any;
        property_index: number;
        property_value?: number;
        property_growth?: number;
        debt?: number;
        debt_ir?: number;
        debt_years?: number;
        initial_debt_years?: number;
        additional_debt_repayments?: number;
        additional_debt_repayments_start_age?: number;
        additional_debt_repayments_end_age?: number;
        include_property_debt?: boolean;
        show_purchase_details?: boolean;
        purchase_age?: number;
        deposit_amount?: number;
        deposit_sources?: {
          savings?: number;
          investments?: number;
          main_kiwisaver?: number;
          partner_kiwisaver?: number;
          gifting?: number;
          other?: number;
        };
        downsize?: boolean;
        downsize_age?: number;
        new_property_value?: number;
        starting_age?: number;
        sell_main_property?: boolean;
        main_property_sale_age?: number;
        main_prop_sale_value?: number;
        pay_off_debt?: boolean;
        sale_allocate_to_investment?: boolean;
        interest_only_period?: boolean;
        interest_only_start_age?: number;
        interest_only_end_age?: number;
        rental_income?: boolean;
        rental_amount?: number;
        rental_start_age?: number;
        rental_end_age?: number;
        board_income?: boolean;
        board_amount?: number;
        board_start_age?: number;
        board_end_age?: number;
        lump_sum_payment_age?: number;
        lump_sum_payment_amount?: number;
        lump_sum_payment_source?: string;
      }

      // Process properties data from JSONB field if it exists
      const propertiesData: PropertyData[] = Array.isArray(scenarioData.properties_data)
        ? (scenarioData.properties_data as PropertyData[])
        : [];

      // Process investment metrics from JSONB if available
      let investmentMetrics = null;
      if (scenarioData.investment_metrics) {
        try {
          // Parse the investment metrics if it's a string, otherwise use as is
          investmentMetrics = typeof scenarioData.investment_metrics === 'string'
            ? JSON.parse(scenarioData.investment_metrics)
            : scenarioData.investment_metrics;

        } catch (error) {
          console.error("Error parsing investment metrics:", error);
        }
      }

      // Initialize property-related variables with proper typing
      let property1Data: PropertyData = {
        property_title: '',
        property_index: 1,
        property_value: 0,
        property_growth: 0,
        debt: 0,
        debt_ir: 0,
        initial_debt_years: 30,
        additional_debt_repayments: 0,
        additional_debt_repayments_start_age: 0,
        additional_debt_repayments_end_age: 0,
        include_property_debt: false,
        purchase_age: 0,
        deposit_amount: 0,
        deposit_sources: {},
        downsize: false,
        downsize_age: 0,
        new_property_value: 0,
        starting_age: 0,
        sell_main_property: false,
        main_property_sale_age: 0,
        main_prop_sale_value: 0,
        pay_off_debt: false,
        sale_allocate_to_investment: false,
        interest_only_period: false,
        interest_only_start_age: 0,
        interest_only_end_age: 0,
        rental_income: false,
        rental_amount: 0,
        rental_start_age: 0,
        rental_end_age: 0,
        board_income: false,
        board_amount: 0,
        board_start_age: 0,
        board_end_age: 0,
        lump_sum_payment_age: 0,
        lump_sum_payment_amount: 0,
        lump_sum_payment_source: ''
      };
      let property2Data: PropertyData = {
        property_title: '',
        property_index: 2,
        property_value: 0,
        property_growth: 0,
        debt: 0,
        debt_ir: 0,
        initial_debt_years: 30,
        additional_debt_repayments: 0,
        additional_debt_repayments_start_age: 0,
        additional_debt_repayments_end_age: 0,
        include_property_debt: false,
        purchase_age: 0,
        deposit_amount: 0,
        deposit_sources: {},
        downsize: false,
        downsize_age: 0,
        new_property_value: 0,
        starting_age: 0,
        sell_main_property: false,
        main_property_sale_age: 0,
        main_prop_sale_value: 0,
        pay_off_debt: false,
        sale_allocate_to_investment: false,
        interest_only_period: false,
        interest_only_start_age: 0,
        interest_only_end_age: 0,
        rental_income: false,
        rental_amount: 0,
        rental_start_age: 0,
        rental_end_age: 0,
        board_income: false,
        board_amount: 0,
        board_start_age: 0,
        board_end_age: 0,
        lump_sum_payment_age: 0,
        lump_sum_payment_amount: 0,
        lump_sum_payment_source: ''
      };
      let property3Data: PropertyData = {
        property_title: '',
        property_index: 3,
        property_value: 0,
        property_growth: 0,
        debt: 0,
        debt_ir: 0,
        initial_debt_years: 30,
        additional_debt_repayments: 0,
        additional_debt_repayments_start_age: 0,
        additional_debt_repayments_end_age: 0,
        include_property_debt: false,
        purchase_age: 0,
        deposit_amount: 0,
        deposit_sources: {},
        downsize: false,
        downsize_age: 0,
        new_property_value: 0,
        starting_age: 0,
        sell_main_property: false,
        main_property_sale_age: 0,
        main_prop_sale_value: 0,
        pay_off_debt: false,
        sale_allocate_to_investment: false,
        interest_only_period: false,
        interest_only_start_age: 0,
        interest_only_end_age: 0,
        rental_income: false,
        rental_amount: 0,
        rental_start_age: 0,
        rental_end_age: 0,
        board_income: false,
        board_amount: 0,
        board_start_age: 0,
        board_end_age: 0,
        lump_sum_payment_age: 0,
        lump_sum_payment_amount: 0,
        lump_sum_payment_source: ''
      };
      let property4Data: PropertyData = {
        property_title: '',
        property_index: 4,
        property_value: 0,
        property_growth: 0,
        debt: 0,
        debt_ir: 0,
        initial_debt_years: 30,
        additional_debt_repayments: 0,
        additional_debt_repayments_start_age: 0,
        additional_debt_repayments_end_age: 0,
        include_property_debt: false,
        purchase_age: 0,
        deposit_amount: 0,
        deposit_sources: {},
        downsize: false,
        downsize_age: 0,
        new_property_value: 0,
        starting_age: 0,
        sell_main_property: false,
        main_property_sale_age: 0,
        main_prop_sale_value: 0,
        pay_off_debt: false,
        sale_allocate_to_investment: false,
        interest_only_period: false,
        interest_only_start_age: 0,
        interest_only_end_age: 0,
        rental_income: false,
        rental_amount: 0,
        rental_start_age: 0,
        rental_end_age: 0,
        board_income: false,
        board_amount: 0,
        board_start_age: 0,
        board_end_age: 0,
        lump_sum_payment_age: 0,
        lump_sum_payment_amount: 0,
        lump_sum_payment_source: ''
      };
      let property5Data: PropertyData = {
        property_title: '',
        property_index: 5,
        property_value: 0,
        property_growth: 0,
        debt: 0,
        debt_ir: 0,
        initial_debt_years: 30,
        additional_debt_repayments: 0,
        additional_debt_repayments_start_age: 0,
        additional_debt_repayments_end_age: 0,
        include_property_debt: false,
        purchase_age: 0,
        deposit_amount: 0,
        deposit_sources: {},
        downsize: false,
        downsize_age: 0,
        new_property_value: 0,
        starting_age: 0,
        sell_main_property: false,
        main_property_sale_age: 0,
        main_prop_sale_value: 0,
        pay_off_debt: false,
        sale_allocate_to_investment: false,
        interest_only_period: false,
        interest_only_start_age: 0,
        interest_only_end_age: 0,
        rental_income: false,
        rental_amount: 0,
        rental_start_age: 0,
        rental_end_age: 0,
        board_income: false,
        board_amount: 0,
        board_start_age: 0,
        board_end_age: 0,
        lump_sum_payment_age: 0,
        lump_sum_payment_amount: 0,
        lump_sum_payment_source: ''
      };

      // Extract data for each property based on property_index
      propertiesData.forEach((property: PropertyData) => {
        if (property.property_index === 1) {
          property1Data = property;
        } else if (property.property_index === 2) {
          property2Data = property;
        } else if (property.property_index === 3) {
          property3Data = property;
        } else if (property.property_index === 4) {
          property4Data = property;
        } else if (property.property_index === 5) {
          property5Data = property;
        }
      });

      const inputData: InputData = {
        property_expenses: scenarioData.property_expenses || '',
        savings_owner: scenarioData.name,
        household_name: scenarioData.household_name,
        name: scenarioData.scenario_name,
        whatIfEvents: Array.isArray(scenarioData.what_if) ? scenarioData.what_if : [],
        starting_age: scenarioData.starting_age || 0,
        ending_age: scenarioData.ending_age || 100,
        annual_income: scenarioData.annual_income || 0,
        income_period: income_period,
        partner_starting_age: scenarioData.partner_starting_age || 0,
        partner_annual_income: scenarioData.partner_annual_income || 0,
        partner_income_period: partner_income_period,
        superannuation: scenarioData.include_superannuation || false,
        includePartner: scenarioData.includePartner !== undefined ? scenarioData.includePartner : (scenarioData.partner_starting_age !== undefined && scenarioData.partner_starting_age !== null),
        savings_amount: scenarioData.savings_amount || 0,
        cash_reserve: scenarioData.cash_reserve || 0,
        utilise_excess_cashflow: scenarioData.utilise_excess_cashflow || false,
        saving_percentage: scenarioData.saving_percentage || 0,
        annual_expenses1: scenarioData.annual_expenses1 || 0,
        expense_period1: expense_period1,
        second_expense: !!scenarioData.annual_expenses2,
        annual_expenses2: scenarioData.annual_expenses2 || 0,
        expense_period2: expense_period2,
        additional_incomes: Array.isArray(scenarioData.additional_incomes)
          ? scenarioData.additional_incomes
          : [],
        additional_expenses: Array.isArray(scenarioData.additional_expenses)
          ? scenarioData.additional_expenses
          : [],
        one_off_investments: Array.isArray(scenarioData.one_off_investments)
          ? scenarioData.one_off_investments.map((investment: any) => ({
            amount: investment.amount || 0,
            age: investment.age || 0,
            details: investment.details
          }))
          : [],
        // Investment funds - load from investment_metrics JSONB if available
        ...(investmentMetrics ? {
          // Legacy investment fund
          initial_investment: investmentMetrics.legacy?.initial_investment || scenarioData.initial_investment || 0,
          annual_investment_contribution: investmentMetrics.legacy?.annual_investment_contribution || scenarioData.annual_investment_contribution || 0,
          annual_investment_return: investmentMetrics.legacy?.annual_investment_return || scenarioData.annual_investment_return || 5,
          inv_std_dev: investmentMetrics.legacy?.inv_std_dev || scenarioData.inv_std_dev || 0,
          investment_return_period: investmentMetrics.legacy?.investment_return_period || investment_return_period,
          investment_description: investmentMetrics.legacy?.investment_description || scenarioData.investment_description || '',
          inv_tax: investmentMetrics.legacy?.inv_tax || scenarioData.inv_tax || 'PIE',

          // Individual investment funds
          initial_investment1: investmentMetrics.funds?.fund1?.initial_investment || scenarioData.initial_investment1 || 0,
          annual_investment_contribution1: investmentMetrics.funds?.fund1?.annual_investment_contribution || scenarioData.annual_investment_contribution1 || 0,
          annual_investment_return1: investmentMetrics.funds?.fund1?.annual_investment_return || scenarioData.annual_investment_return1 || 5.5,
          inv_std_dev1: investmentMetrics.funds?.fund1?.inv_std_dev || scenarioData.inv_std_dev1 || 8.0,
          investment_description1: investmentMetrics.funds?.fund1?.investment_description || scenarioData.investment_description1 || 'Investment Fund 1',
          fund_periods1: investmentMetrics.funds?.fund1?.fund_periods || [],

          initial_investment2: investmentMetrics.funds?.fund2?.initial_investment || scenarioData.initial_investment2 || 0,
          annual_investment_contribution2: investmentMetrics.funds?.fund2?.annual_investment_contribution || scenarioData.annual_investment_contribution2 || 0,
          annual_investment_return2: investmentMetrics.funds?.fund2?.annual_investment_return || scenarioData.annual_investment_return2 || 5.5,
          inv_std_dev2: investmentMetrics.funds?.fund2?.inv_std_dev || scenarioData.inv_std_dev2 || 8.0,
          investment_description2: investmentMetrics.funds?.fund2?.investment_description || scenarioData.investment_description2 || 'Investment Fund 2',
          fund_periods2: investmentMetrics.funds?.fund2?.fund_periods || [],

          initial_investment3: investmentMetrics.funds?.fund3?.initial_investment || scenarioData.initial_investment3 || 0,
          annual_investment_contribution3: investmentMetrics.funds?.fund3?.annual_investment_contribution || scenarioData.annual_investment_contribution3 || 0,
          annual_investment_return3: investmentMetrics.funds?.fund3?.annual_investment_return || scenarioData.annual_investment_return3 || 5.5,
          inv_std_dev3: investmentMetrics.funds?.fund3?.inv_std_dev || scenarioData.inv_std_dev3 || 8.0,
          investment_description3: investmentMetrics.funds?.fund3?.investment_description || scenarioData.investment_description3 || 'Investment Fund 3',
          fund_periods3: investmentMetrics.funds?.fund3?.fund_periods || [],

          initial_investment4: investmentMetrics.funds?.fund4?.initial_investment || scenarioData.initial_investment4 || 0,
          annual_investment_contribution4: investmentMetrics.funds?.fund4?.annual_investment_contribution || scenarioData.annual_investment_contribution4 || 0,
          annual_investment_return4: investmentMetrics.funds?.fund4?.annual_investment_return || scenarioData.annual_investment_return4 || 5.5,
          inv_std_dev4: investmentMetrics.funds?.fund4?.inv_std_dev || scenarioData.inv_std_dev4 || 8.0,
          investment_description4: investmentMetrics.funds?.fund4?.investment_description || scenarioData.investment_description4 || 'Investment Fund 4',
          fund_periods4: investmentMetrics.funds?.fund4?.fund_periods || [],

          initial_investment5: investmentMetrics.funds?.fund5?.initial_investment || scenarioData.initial_investment5 || 0,
          annual_investment_contribution5: investmentMetrics.funds?.fund5?.annual_investment_contribution || scenarioData.annual_investment_contribution5 || 0,
          annual_investment_return5: investmentMetrics.funds?.fund5?.annual_investment_return || scenarioData.annual_investment_return5 || 5.5,
          inv_std_dev5: investmentMetrics.funds?.fund5?.inv_std_dev || scenarioData.inv_std_dev5 || 8.0,
          investment_description5: investmentMetrics.funds?.fund5?.investment_description || scenarioData.investment_description5 || 'Investment Fund 5',
          fund_periods5: investmentMetrics.funds?.fund5?.fund_periods || [],

          // Withdrawal priorities
          withdrawal_priorities: investmentMetrics.withdrawal_priorities || [1]
        } : {
          // Fallback to legacy fields if no investment_metrics
          initial_investment: scenarioData.initial_investment || 0,
          annual_investment_contribution: scenarioData.annual_investment_contribution || 0,
          annual_investment_return: scenarioData.annual_investment_return || 5,
          inv_std_dev: scenarioData.inv_std_dev || 0,
          investment_return_period: investment_return_period,
          investment_description: scenarioData.investment_description || '',
          inv_tax: scenarioData.inv_tax || 'PIE',

          initial_investment1: scenarioData.initial_investment1 || 0,
          annual_investment_contribution1: scenarioData.annual_investment_contribution1 || 0,
          annual_investment_return1: scenarioData.annual_investment_return1 || 5.5,
          inv_std_dev1: scenarioData.inv_std_dev1 || 8.0,
          investment_description1: scenarioData.investment_description1 || 'Investment Fund 1',

          initial_investment2: scenarioData.initial_investment2 || 0,
          annual_investment_contribution2: scenarioData.annual_investment_contribution2 || 0,
          annual_investment_return2: scenarioData.annual_investment_return2 || 5.5,
          inv_std_dev2: scenarioData.inv_std_dev2 || 8.0,
          investment_description2: scenarioData.investment_description2 || 'Investment Fund 2',

          initial_investment3: scenarioData.initial_investment3 || 0,
          annual_investment_contribution3: scenarioData.annual_investment_contribution3 || 0,
          annual_investment_return3: scenarioData.annual_investment_return3 || 5.5,
          inv_std_dev3: scenarioData.inv_std_dev3 || 8.0,
          investment_description3: scenarioData.investment_description3 || 'Investment Fund 3',

          initial_investment4: scenarioData.initial_investment4 || 0,
          annual_investment_contribution4: scenarioData.annual_investment_contribution4 || 0,
          annual_investment_return4: scenarioData.annual_investment_return4 || 5.5,
          inv_std_dev4: scenarioData.inv_std_dev4 || 8.0,
          investment_description4: scenarioData.investment_description4 || 'Investment Fund 4',

          initial_investment5: scenarioData.initial_investment5 || 0,
          annual_investment_contribution5: scenarioData.annual_investment_contribution5 || 0,
          annual_investment_return5: scenarioData.annual_investment_return5 || 5.5,
          inv_std_dev5: scenarioData.inv_std_dev5 || 8.0,
          investment_description5: scenarioData.investment_description5 || 'Investment Fund 5',

          withdrawal_priorities: scenarioData.withdrawal_priorities || [1]
        }),
        initial_kiwiSaver: scenarioData.initial_kiwisaver || 0,
        kiwisaver_contribution: scenarioData.kiwisaver_contribution ?? 3,
        employer_contribution: scenarioData.employer_contribution ?? 3,
        annual_kiwisaver_return: scenarioData.annual_kiwisaver_return || 5,
        ks_std_dev: scenarioData.ks_std_dev || 0,
        partner_initial_kiwisaver: scenarioData.partner_initial_kiwisaver || 0,
        partner_kiwisaver_contribution: scenarioData.partner_kiwisaver_contribution ?? 3,
        partner_employer_contribution: scenarioData.partner_employer_contribution ?? 3,
        partner_annual_kiwisaver_return: scenarioData.partner_annual_kiwisaver_return || 5,
        partner_ks_std_dev: scenarioData.partner_ks_std_dev || 0,
        property_value: property1Data?.property_value || scenarioData.property_value || 0,
        property_title: property1Data?.property_title || scenarioData.property_title || '',
        debt: property1Data?.debt || scenarioData.debt || 0,
        property_growth: property1Data?.property_growth || scenarioData.property_growth || 0,
        downsize: property1Data?.downsize || scenarioData.downsize || false,
        sale_allocate_to_investment: property1Data?.sale_allocate_to_investment || scenarioData.sale_allocate_to_investment || false,
        sale_investment_fund: scenarioData.sale_investment_fund || '',
        downsize_age: property1Data?.downsize_age || scenarioData.downsize_age || 0,
        new_property_value: property1Data?.new_property_value || scenarioData.new_property_value || 0,
        fund_diversion: scenarioData.fund_diversion || 0,
        debt_ir: property1Data?.debt_ir || scenarioData.debt_ir || 0,
        initial_debt_years: property1Data?.initial_debt_years || scenarioData.initial_debt_years || 30,
        additional_debt_repayments: property1Data?.additional_debt_repayments || scenarioData.additional_debt_repayments || 0,
        additional_debt_repayments_start_age: property1Data?.additional_debt_repayments_start_age || scenarioData.additional_debt_repayments_start_age || 0,
        additional_debt_repayments_end_age: property1Data?.additional_debt_repayments_end_age || scenarioData.additional_debt_repayments_end_age || 0,
        include_property_debt: scenarioData.include_property_debt || false,
        lump_sum_payment_age: scenarioData.lump_sum_payment_age || undefined,
        lump_sum_payment_amount: scenarioData.lump_sum_payment_amount || undefined,
        lump_sum_payment_source: (scenarioData.lump_sum_payment_source || undefined) as 'investments' | 'savings' | undefined,
        inflation_rate: scenarioData.inflation_rate || 0,
        expense1_inflation_rate: scenarioData.expense1_inflation_rate || 0,
        expense2_inflation_rate: scenarioData.expense2_inflation_rate || 0,
        main_income_inflation_rate: scenarioData.main_income_inflation_rate || scenarioData.inflation_rate || 2.0,
        partner_income_inflation_rate: scenarioData.partner_income_inflation_rate || scenarioData.inflation_rate || 2.0,
        sell_main_property: property1Data?.sell_main_property || false,
        main_property_sale_age: property1Data?.main_property_sale_age || 0,
        main_prop_sale_value: property1Data?.main_prop_sale_value || 0,
        pay_off_debt: property1Data?.pay_off_debt || false,
        show_monte_carlo: false,
        num_simulations: scenarioData.simulations || 100,
        confidence_interval: scenarioData.confidence_interval || 90,
        worst_scenario: [],
        best_scenario: [],
        average_scenario: [],
        show_repayments: false,
        show_cashflow_breakdown: false,
        partner_name: scenarioData.partner_name || '',
        household_id: scenarioData.household_id,
        seed: scenarioData.seed || 0,
        inv_fund_type: getFundType(scenarioData.fund_type),
        ks_fund_type: getFundType(scenarioData.ks_fund_type),
        partner_ks_fund_type: scenarioData.partner_ks_fund_type ? getFundType(scenarioData.partner_ks_fund_type) : undefined,

        // Fund periods - load from investment_metrics if available
        fund_periods: Array.isArray(scenarioData.fund_periods) ? scenarioData.fund_periods : [],

        ks_periods: Array.isArray(scenarioData.ks_periods) ? scenarioData.ks_periods : [],
        partner_ks_periods: Array.isArray(scenarioData.partner_ks_periods) ? scenarioData.partner_ks_periods : [],
        consolidate_kiwisaver: scenarioData.consolidate_kiwisaver,
        consolidate_kiwisaver_age: scenarioData.consolidate_kiwisaver_age,
        // Load KiwiSaver consolidation allocations
        consolidation_allocations: Array.isArray(scenarioData.consolidation_allocations) ? scenarioData.consolidation_allocations : [],
        partner_consolidate_kiwisaver: scenarioData.partner_consolidate_kiwisaver,
        partner_consolidate_kiwisaver_age: scenarioData.partner_consolidate_kiwisaver_age,
        // Load partner KiwiSaver consolidation allocations
        partner_consolidation_allocations: Array.isArray(scenarioData.partner_consolidation_allocations) ? scenarioData.partner_consolidation_allocations : [],
        allocate_to_investment: scenarioData.allocate_to_investment || false,
        contribution_period: contribution_period,
        // Property 2 fields
        property_value2: property2Data?.property_value || 0,
        property_title2: property2Data?.property_title || '',
        property_growth2: property2Data?.property_growth || 0,
        debt2: property2Data?.debt || 0,
        debt_ir2: property2Data?.debt_ir || 0,
        debt_years2: property2Data?.debt_years || 0,
        initial_debt_years2: property2Data?.initial_debt_years || 0,
        additional_debt_repayments2: property2Data?.additional_debt_repayments || 0,
        additional_debt_repayments_start_age2: property2Data?.additional_debt_repayments_start_age || 0,
        additional_debt_repayments_end_age2: property2Data?.additional_debt_repayments_end_age || 0,
        include_property_debt2: property2Data?.include_property_debt || false,
        sale2_allocate_to_investment: property2Data?.sale_allocate_to_investment || false,
        is_second_property: !!property2Data,
        sell_property: false,

        // Property 3 fields
        property_value3: property3Data?.property_value || 0,
        property_title3: property3Data?.property_title || '',
        property_growth3: property3Data?.property_growth || 0,
        debt3: property3Data?.debt || 0,
        debt_ir3: property3Data?.debt_ir || 0,
        debt_years3: property3Data?.debt_years || 0,
        initial_debt_years3: property3Data?.initial_debt_years || 0,
        additional_debt_repayments3: property3Data?.additional_debt_repayments || 0,
        additional_debt_repayments_start_age3: property3Data?.additional_debt_repayments_start_age || 0,
        additional_debt_repayments_end_age3: property3Data?.additional_debt_repayments_end_age || 0,
        starting_age3: property3Data?.starting_age || 0,
        downsize_age3: property3Data?.downsize_age || 0,
        new_property_value3: property3Data?.new_property_value || 0,
        sale3_allocate_to_investment: property3Data?.sale_allocate_to_investment || false,

        // Property 4 fields
        property_value4: property4Data?.property_value || 0,
        property_title4: property4Data?.property_title || '',
        property_growth4: property4Data?.property_growth || 0,
        debt4: property4Data?.debt || 0,
        debt_ir4: property4Data?.debt_ir || 0,
        debt_years4: property4Data?.debt_years || 0,
        initial_debt_years4: property4Data?.initial_debt_years || 0,
        additional_debt_repayments4: property4Data?.additional_debt_repayments || 0,
        additional_debt_repayments_start_age4: property4Data?.additional_debt_repayments_start_age || 0,
        additional_debt_repayments_end_age4: property4Data?.additional_debt_repayments_end_age || 0,
        starting_age4: property4Data?.starting_age || 0,
        downsize_age4: property4Data?.downsize_age || 0,
        new_property_value4: property4Data?.new_property_value || 0,
        sale4_allocate_to_investment: property4Data?.sale_allocate_to_investment || false,

        // Property 5 fields
        property_value5: property5Data?.property_value || 0,
        property_title5: property5Data?.property_title || '',
        property_growth5: property5Data?.property_growth || 0,
        debt5: property5Data?.debt || 0,
        debt_ir5: property5Data?.debt_ir || 0,
        debt_years5: property5Data?.debt_years || 0,
        initial_debt_years5: property5Data?.initial_debt_years || 0,
        additional_debt_repayments5: property5Data?.additional_debt_repayments || 0,
        additional_debt_repayments_start_age5: property5Data?.additional_debt_repayments_start_age || 0,
        additional_debt_repayments_end_age5: property5Data?.additional_debt_repayments_end_age || 0,
        starting_age5: property5Data?.starting_age || 0,
        downsize_age5: property5Data?.downsize_age || 0,
        new_property_value5: property5Data?.new_property_value || 0,
        sale5_allocate_to_investment: property5Data?.sale_allocate_to_investment || false,

        // Purchase details for property 1
        show_purchase_details: property1Data?.show_purchase_details || false,
        purchase_age: property1Data?.purchase_age || 0,
        deposit_amount: property1Data?.deposit_amount || 0,
        deposit_sources: property1Data?.deposit_sources || {},

        // Purchase details for property 2
        show_purchase_details2: property2Data?.show_purchase_details || false,
        purchase_age2: property2Data?.purchase_age || 0,
        deposit_amount2: property2Data?.deposit_amount || 0,
        deposit_sources2: property2Data?.deposit_sources || {},

        // Purchase details for property 3
        show_purchase_details3: property3Data?.show_purchase_details || false,
        purchase_age3: property3Data?.purchase_age || 0,
        deposit_amount3: property3Data?.deposit_amount || 0,
        deposit_sources3: property3Data?.deposit_sources || {},

        // Purchase details for property 4
        show_purchase_details4: property4Data?.show_purchase_details || false,
        purchase_age4: property4Data?.purchase_age || 0,
        deposit_amount4: property4Data?.deposit_amount || 0,
        deposit_sources4: property4Data?.deposit_sources || {},

        // Purchase details for property 5
        show_purchase_details5: property5Data?.show_purchase_details || false,
        purchase_age5: property5Data?.purchase_age || 0,
        deposit_amount5: property5Data?.deposit_amount || 0,
        deposit_sources5: property5Data?.deposit_sources || {},

        // Interest-only periods for property 1
        interest_only_period: property1Data?.interest_only_period || scenarioData.interest_only_period || false,
        interest_only_start_age: property1Data?.interest_only_start_age || scenarioData.interest_only_start_age || 0,
        interest_only_end_age: property1Data?.interest_only_end_age || scenarioData.interest_only_end_age || 0,

        // Interest-only periods for property 2
        interest_only_period2: property2Data?.interest_only_period || false,
        interest_only_start_age2: property2Data?.interest_only_start_age || 0,
        interest_only_end_age2: property2Data?.interest_only_end_age || 0,

        // Interest-only periods for property 3
        interest_only_period3: property3Data?.interest_only_period || false,
        interest_only_start_age3: property3Data?.interest_only_start_age || 0,
        interest_only_end_age3: property3Data?.interest_only_end_age || 0,

        // Interest-only periods for property 4
        interest_only_period4: property4Data?.interest_only_period || false,
        interest_only_start_age4: property4Data?.interest_only_start_age || 0,
        interest_only_end_age4: property4Data?.interest_only_end_age || 0,

        // Interest-only periods for property 5
        interest_only_period5: property5Data?.interest_only_period || false,
        interest_only_start_age5: property5Data?.interest_only_start_age || 0,
        interest_only_end_age5: property5Data?.interest_only_end_age || 0,

        // Rental/Board income fields for properties
        rental_income: property1Data?.rental_income || scenarioData.rental_income || false,
        rental_amount: property1Data?.rental_amount || scenarioData.rental_amount || 0,
        rental_start_age: property1Data?.rental_start_age || scenarioData.rental_start_age || 0,
        rental_end_age: property1Data?.rental_end_age || scenarioData.rental_end_age || 0,

        rental_income2: property2Data?.rental_income || scenarioData.rental_income2 || false,
        rental_amount2: property2Data?.rental_amount || scenarioData.rental_amount2 || 0,
        rental_start_age2: property2Data?.rental_start_age || scenarioData.rental_start_age2 || 0,
        rental_end_age2: property2Data?.rental_end_age || scenarioData.rental_end_age2 || 0,

        rental_income3: property3Data?.rental_income || scenarioData.rental_income3 || false,
        rental_amount3: property3Data?.rental_amount || scenarioData.rental_amount3 || 0,
        rental_start_age3: property3Data?.rental_start_age || scenarioData.rental_start_age3 || 0,
        rental_end_age3: property3Data?.rental_end_age || scenarioData.rental_end_age3 || 0,

        rental_income4: property4Data?.rental_income || scenarioData.rental_income4 || false,
        rental_amount4: property4Data?.rental_amount || scenarioData.rental_amount4 || 0,
        rental_start_age4: property4Data?.rental_start_age || scenarioData.rental_start_age4 || 0,
        rental_end_age4: property4Data?.rental_end_age || scenarioData.rental_end_age4 || 0,

        rental_income5: property5Data?.rental_income || scenarioData.rental_income5 || false,
        rental_amount5: property5Data?.rental_amount || scenarioData.rental_amount5 || 0,
        rental_start_age5: property5Data?.rental_start_age || scenarioData.rental_start_age5 || 0,
        rental_end_age5: property5Data?.rental_end_age || scenarioData.rental_end_age5 || 0,

        board_income: property1Data?.board_income || scenarioData.board_income || false,
        board_amount: property1Data?.board_amount || scenarioData.board_amount || 0,
        board_start_age: property1Data?.board_start_age || scenarioData.board_start_age || 0,
        board_end_age: property1Data?.board_end_age || scenarioData.board_end_age || 0,

        board_income2: property2Data?.board_income || scenarioData.board_income2 || false,
        board_amount2: property2Data?.board_amount || scenarioData.board_amount2 || 0,
        board_start_age2: property2Data?.board_start_age || scenarioData.board_start_age2 || 0,
        board_end_age2: property2Data?.board_end_age || scenarioData.board_end_age2 || 0,

        board_income3: property3Data?.board_income || scenarioData.board_income3 || false,
        board_amount3: property3Data?.board_amount || scenarioData.board_amount3 || 0,
        board_start_age3: property3Data?.board_start_age || scenarioData.board_start_age3 || 0,
        board_end_age3: property3Data?.board_end_age || scenarioData.board_end_age3 || 0,

        board_income4: property4Data?.board_income || scenarioData.board_income4 || false,
        board_amount4: property4Data?.board_amount || scenarioData.board_amount4 || 0,
        board_start_age4: property4Data?.board_start_age || scenarioData.board_start_age4 || 0,
        board_end_age4: property4Data?.board_end_age || scenarioData.board_end_age4 || 0,

        board_income5: property5Data?.board_income || scenarioData.board_income5 || false,
        board_amount5: property5Data?.board_amount || scenarioData.board_amount5 || 0,
        board_start_age5: property5Data?.board_start_age || scenarioData.board_start_age5 || 0,
        board_end_age5: property5Data?.board_end_age || scenarioData.board_end_age5 || 0,

        // Lump sum payment fields for properties
        lump_sum_payment_age2: property2Data?.lump_sum_payment_age || scenarioData.lump_sum_payment_age2 || 0,
        lump_sum_payment_amount2: property2Data?.lump_sum_payment_amount || scenarioData.lump_sum_payment_amount2 || 0,
        lump_sum_payment_source2: (property2Data?.lump_sum_payment_source || scenarioData.lump_sum_payment_source2 || undefined) as 'investments' | 'savings' | undefined,

        lump_sum_payment_age3: property3Data?.lump_sum_payment_age || scenarioData.lump_sum_payment_age3 || 0,
        lump_sum_payment_amount3: property3Data?.lump_sum_payment_amount || scenarioData.lump_sum_payment_amount3 || 0,
        lump_sum_payment_source3: (property3Data?.lump_sum_payment_source || scenarioData.lump_sum_payment_source3 || undefined) as 'investments' | 'savings' | undefined,

        lump_sum_payment_age4: property4Data?.lump_sum_payment_age || scenarioData.lump_sum_payment_age4 || 0,
        lump_sum_payment_amount4: property4Data?.lump_sum_payment_amount || scenarioData.lump_sum_payment_amount4 || 0,
        lump_sum_payment_source4: (property4Data?.lump_sum_payment_source || scenarioData.lump_sum_payment_source4 || undefined) as 'investments' | 'savings' | undefined,

        lump_sum_payment_age5: property5Data?.lump_sum_payment_age || scenarioData.lump_sum_payment_age5 || 0,
        lump_sum_payment_amount5: property5Data?.lump_sum_payment_amount || scenarioData.lump_sum_payment_amount5 || 0,
        lump_sum_payment_source5: (property5Data?.lump_sum_payment_source || scenarioData.lump_sum_payment_source5 || undefined) as 'investments' | 'savings' | undefined,

        // Property 2 fields
        sell_main_property2: property2Data?.sell_main_property || false,
        main_property_sale_age2: property2Data?.main_property_sale_age || 0,
        main_prop_sale_value2: property2Data?.main_prop_sale_value || 0,
        pay_off_debt2: property2Data?.pay_off_debt || false,

        // Property 3 fields
        sell_main_property3: property3Data?.sell_main_property || false,
        main_property_sale_age3: property3Data?.main_property_sale_age || 0,
        main_prop_sale_value3: property3Data?.main_prop_sale_value || 0,
        pay_off_debt3: property3Data?.pay_off_debt || false,

        // Property 4 fields
        sell_main_property4: property4Data?.sell_main_property || false,
        main_property_sale_age4: property4Data?.main_property_sale_age || 0,
        main_prop_sale_value4: property4Data?.main_prop_sale_value || 0,
        pay_off_debt4: property4Data?.pay_off_debt || false,

        // Property 5 fields
        sell_main_property5: property5Data?.sell_main_property || false,
        main_property_sale_age5: property5Data?.main_property_sale_age || 0,
        main_prop_sale_value5: property5Data?.main_prop_sale_value || 0,
        pay_off_debt5: property5Data?.pay_off_debt || false,
        cash_rate: 0,
        pay_off_debt_on_downsize: undefined
      };

      setInputData(inputData);
      setSelectedScenario(scenarioData);
      setHasPartner(inputData.includePartner);

      const result = calculateFinancialLife(inputData as unknown as FinancialData);
      setAllMetrics(result.allMetrics);
      setChanceOfSuccess(result.chanceOfSuccess);
      setSuccessfulScenarios(result.successfulScenarios);
      setFailedScenarios(result.failedScenarios);
      setMinNetWealthAtAge(result.worstNetWealthAtAge);
      setMaxNetWealthAtAge(result.bestNetWealthAtAge);
      setAverageNetWealthAtAge(result.averageNetWealthAtAge);
    }
    setIsLoading(false);

    // We don't set dataReady to true here anymore
    // This is now handled in the handleScenarioChange function
    // to ensure proper timing of animations
  };

  const processScenarioData = (scenarioData: any): InputData => {
    return {
      cash_rate: scenarioData.cash_rate || 0,
      initial_debt_years2: scenarioData.initial_debt_years2 || 0,
      initial_debt_years3: scenarioData.initial_debt_years3 || 0,
      initial_debt_years4: scenarioData.initial_debt_years4 || 0,
      initial_debt_years5: scenarioData.initial_debt_years5 || 0,
      property_expenses: scenarioData.property_expenses || '',
      savings_owner: scenarioData.name,
      household_name: scenarioData.household_name,
      pay_off_debt_on_downsize: scenarioData.pay_off_debt_on_downsize || false,
      name: scenarioData.scenario_name,
      starting_age: scenarioData.starting_age || 0,
      ending_age: scenarioData.ending_age || 0,
      annual_income: scenarioData.annual_income || 0,
      income_period: [scenarioData.income_period_start || 0, scenarioData.income_period_end || 0] as [number, number],
      partner_starting_age: scenarioData.partner_starting_age || 0,
      partner_annual_income: scenarioData.partner_annual_income || 0,
      partner_income_period: [scenarioData.partner_income_period_start || 0, scenarioData.partner_income_period_end || 0] as [number, number],
      superannuation: scenarioData.include_superannuation || false,
      includePartner: scenarioData.includePartner !== undefined ? scenarioData.includePartner : (scenarioData.partner_starting_age !== undefined && scenarioData.partner_starting_age !== null),
      savings_amount: scenarioData.savings_amount || 0,
      cash_reserve: scenarioData.cash_reserve || 0,
      utilise_excess_cashflow: scenarioData.utilise_excess_cashflow || false,
      saving_percentage: scenarioData.saving_percentage || 0,
      annual_expenses1: scenarioData.annual_expenses1 || 0,
      expense_period1: [scenarioData.expense_period1_start || 0, scenarioData.expense_period1_end || 0] as [number, number],
      second_expense: !!scenarioData.annual_expenses2,
      annual_expenses2: scenarioData.annual_expenses2 || 0,
      expense_period2: [scenarioData.expense_period2_start || 0, scenarioData.expense_period2_end || 0] as [number, number],
      additional_incomes: Array.isArray(scenarioData.additional_incomes)
        ? scenarioData.additional_incomes.map((income: any) => ({
            title: income.title,
            value: income.value,
            period: income.period,
            tax_type: income.tax_type,
            inflation_rate: income.inflation_rate
          }))
        : [],
      additional_expenses: Array.isArray(scenarioData.additional_expenses)
        ? scenarioData.additional_expenses.map((expense: any) => ({
            title: expense.title,
            value: expense.value,
            period: expense.period
          }))
        : [],
      one_off_investments: Array.isArray(scenarioData.one_off_investments)
        ? scenarioData.one_off_investments.map((investment: any) => ({
            amount: investment.amount || 0,
            age: investment.age || 0,
            details: investment.details
          }))
        : [],
      // Main investment fund (fund 1)
      initial_investment: scenarioData.initial_investment || 0,
      annual_investment_contribution: scenarioData.annual_investment_contribution || 0,
      annual_investment_return: scenarioData.annual_investment_return || 0,
      inv_std_dev: scenarioData.inv_std_dev || 0,
      investment_return_period: scenarioData.investment_return_period || [scenarioData.starting_age, scenarioData.ending_age] as [number, number],
      investment_description: scenarioData.investment_description || '',
      inv_tax: '0%',

      // Investment fund 1
      initial_investment1: scenarioData.initial_investment1 || 0,
      annual_investment_contribution1: scenarioData.annual_investment_contribution1 || 0,
      annual_investment_return1: scenarioData.annual_investment_return1 || 5.5,
      inv_std_dev1: scenarioData.inv_std_dev1 || 8.0,
      investment_description1: scenarioData.investment_description1 || 'Investment Fund 1',

      // Investment fund 2
      initial_investment2: scenarioData.initial_investment2 || 0,
      annual_investment_contribution2: scenarioData.annual_investment_contribution2 || 0,
      annual_investment_return2: scenarioData.annual_investment_return2 || 5.5,
      inv_std_dev2: scenarioData.inv_std_dev2 || 8.0,
      investment_description2: scenarioData.investment_description2 || 'Investment Fund 2',

      // Investment fund 3
      initial_investment3: scenarioData.initial_investment3 || 0,
      annual_investment_contribution3: scenarioData.annual_investment_contribution3 || 0,
      annual_investment_return3: scenarioData.annual_investment_return3 || 5.5,
      inv_std_dev3: scenarioData.inv_std_dev3 || 8.0,
      investment_description3: scenarioData.investment_description3 || 'Investment Fund 3',

      // Investment fund 4
      initial_investment4: scenarioData.initial_investment4 || 0,
      annual_investment_return4: scenarioData.annual_investment_return4 || 5.5,
      annual_investment_contribution4: scenarioData.annual_investment_contribution4 || 0,
      inv_std_dev4: scenarioData.inv_std_dev4 || 8.0,
      investment_description4: scenarioData.investment_description4 || 'Investment Fund 4',

      // Investment fund 5
      initial_investment5: scenarioData.initial_investment5 || 0,
      annual_investment_contribution5: scenarioData.annual_investment_contribution5 || 0,
      annual_investment_return5: scenarioData.annual_investment_return5 || 5.5,
      inv_std_dev5: scenarioData.inv_std_dev5 || 8.0,
      investment_description5: scenarioData.investment_description5 || 'Investment Fund 5',
      initial_kiwiSaver: scenarioData.initial_kiwisaver || 0,
      kiwisaver_contribution: scenarioData.kiwisaver_contribution || 0,
      employer_contribution: scenarioData.employer_contribution || 0,
      annual_kiwisaver_return: scenarioData.annual_kiwisaver_return || 0,
      ks_std_dev: scenarioData.ks_std_dev || 0,
      partner_initial_kiwisaver: scenarioData.partner_initial_kiwisaver || 0,
      partner_kiwisaver_contribution: scenarioData.partner_kiwisaver_contribution || 0,
      partner_employer_contribution: scenarioData.partner_employer_contribution || 0,
      partner_annual_kiwisaver_return: scenarioData.partner_annual_kiwisaver_return || 0,
      partner_ks_std_dev: scenarioData.partner_ks_std_dev || 0,
          property_value: scenarioData.property_value || 0,
      property_title: scenarioData.property_title || '',
          debt: scenarioData.debt || 0,
          property_growth: scenarioData.property_growth || 0,
      downsize: scenarioData.downsize || false,
          sale_allocate_to_investment: scenarioData.sale_allocate_to_investment || false,
      sale_investment_fund: scenarioData.sale_investment_fund || '',
          downsize_age: scenarioData.downsize_age || 0,
          new_property_value: scenarioData.new_property_value || 0,
      fund_diversion: scenarioData.fund_diversion || 0,
      debt_ir: scenarioData.debt_ir || 0,
      initial_debt_years: scenarioData.initial_debt_years || 0,
      additional_debt_repayments: scenarioData.additional_debt_repayments || 0,
      include_property_debt: scenarioData.include_property_debt || false,
      lump_sum_payment_age: scenarioData.lump_sum_payment_age || undefined,
      lump_sum_payment_amount: scenarioData.lump_sum_payment_amount || undefined,
      lump_sum_payment_source: (scenarioData.lump_sum_payment_source || undefined) as 'investments' | 'savings' | undefined,
      inflation_rate: scenarioData.inflation_rate || 2.0,
      expense1_inflation_rate: scenarioData.expense1_inflation_rate || 0,
      expense2_inflation_rate: scenarioData.expense2_inflation_rate || 0,
      main_income_inflation_rate: scenarioData.main_income_inflation_rate || scenarioData.inflation_rate || 2.0,
      partner_income_inflation_rate: scenarioData.partner_income_inflation_rate || scenarioData.inflation_rate || 2.0,
      sell_main_property: false,
      sell_main_property2: false,
      sell_main_property3: false,
      sell_main_property4: false,
      sell_main_property5: false,
      property_title2: '',
      property_title3: '',
      property_title4: '',
      property_title5: '',
      main_property_sale_age: 0,
      main_property_sale_age2: 0,
      main_property_sale_age3: 0,
      main_property_sale_age4: 0,
      main_property_sale_age5: 0,
      main_prop_sale_value: 0,
      main_prop_sale_value2: 0,
      main_prop_sale_value3: 0,
      main_prop_sale_value4: 0,
      main_prop_sale_value5: 0,
      pay_off_debt: false,
      pay_off_debt2: false,
      pay_off_debt3: false,
      pay_off_debt4: false,
      pay_off_debt5: false,
      show_monte_carlo: false,
      num_simulations: scenarioData.simulations || 100,
      confidence_interval: scenarioData.confidence_interval || 90,
      worst_scenario: [],
      best_scenario: [],
      average_scenario: [],
      show_repayments: false,
      show_cashflow_breakdown: false,
      partner_name: scenarioData.partner_name || '',
      household_id: scenarioData.household_id,
      seed: Date.now(),
      inv_fund_type: 'Equity',
      ks_fund_type: 'Equity',
      partner_ks_fund_type: 'Equity',
      fund_periods: scenarioData.fund_periods || [0, 0] as [number, number],
      contribution_period: [scenarioData.contribution_period_start || 0, scenarioData.contribution_period_end || 0] as [number, number],
      allocate_to_investment: scenarioData.allocate_to_investment || false,
      // Property 2 fields
      property_value2: 0,
      property_growth2: 0,
      debt2: 0,
      debt_ir2: 0,
      debt_years2: 0,
      additional_debt_repayments2: 0,
      include_property_debt2: false,
      sale2_allocate_to_investment: false,
      is_second_property: false,
        sell_property: false,

        // Property 3 fields
        property_value3: 0,
        property_growth3: 0,
        debt3: 0,
        debt_ir3: 0,
        debt_years3: 0,
        additional_debt_repayments3: 0,
        starting_age3: 0,
        downsize_age3: 0,
        new_property_value3: 0,
        sale3_allocate_to_investment: false,
        lump_sum_payment_age3: 0,
        lump_sum_payment_amount3: 0,
        lump_sum_payment_source3: (undefined) as 'investments' | 'savings' | undefined,

        // Property 4 fields
        property_value4: 0,
        property_growth4: 0,
        debt4: 0,
        debt_ir4: 0,
        debt_years4: 0,
        additional_debt_repayments4: 0,
        starting_age4: 0,
        downsize_age4: 0,
        new_property_value4: 0,
        sale4_allocate_to_investment: false,
        lump_sum_payment_age4: 0,
        lump_sum_payment_amount4: 0,
        lump_sum_payment_source4: (undefined) as 'investments' | 'savings' | undefined,

        // Property 5 fields
        property_value5: 0,
        property_growth5: 0,
        debt5: 0,
        debt_ir5: 0,
        debt_years5: 0,
        additional_debt_repayments5: 0,
        starting_age5: 0,
        downsize_age5: 0,
        new_property_value5: 0,
        sale5_allocate_to_investment: false,
        lump_sum_payment_age5: 0,
        lump_sum_payment_amount5: 0,
        lump_sum_payment_source5: (undefined) as 'investments' | 'savings' | undefined,

        // Interest-only periods for property 1
        interest_only_period: false,
        interest_only_start_age: 0,
        interest_only_end_age: 0,

        // Interest-only periods for property 2
        interest_only_period2: false,
        interest_only_start_age2: 0,
        interest_only_end_age2: 0,

        // Interest-only periods for property 3
        interest_only_period3: false,
        interest_only_start_age3: 0,
        interest_only_end_age3: 0,

        // Interest-only periods for property 4
        interest_only_period4: false,
        interest_only_start_age4: 0,
        interest_only_end_age4: 0,

        // Interest-only periods for property 5
        interest_only_period5: false,
        interest_only_start_age5: 0,
        interest_only_end_age5: 0,

        // Rental/Board income fields for properties
        rental_income: false,
        rental_amount: 0,
        rental_start_age: 0,
        rental_end_age: 0,

        rental_income2: false,
        rental_amount2: 0,
        rental_start_age2: 0,
        rental_end_age2: 0,

        rental_income3: false,
        rental_amount3: 0,
        rental_start_age3: 0,
        rental_end_age3: 0,

        rental_income4: false,
        rental_amount4: 0,
        rental_start_age4: 0,
        rental_end_age4: 0,

        rental_income5: false,
        rental_amount5: 0,
        rental_start_age5: 0,
        rental_end_age5: 0,

        board_income: false,
        board_amount: 0,
        board_start_age: 0,
        board_end_age: 0,

        board_income2: false,
        board_amount2: 0,
        board_start_age2: 0,
        board_end_age2: 0,

        board_income3: false,
        board_amount3: 0,
        board_start_age3: 0,
        board_end_age3: 0,

        board_income4: false,
        board_amount4: 0,
        board_start_age4: 0,
        board_end_age4: 0,

        board_income5: false,
        board_amount5: 0,
        board_start_age5: 0,
        board_end_age5: 0,
    };
  };

  const handleScenarioSelect = (scenarioData: any) => {
    const hasPartner = scenarioData.partner_starting_age !== undefined && scenarioData.partner_starting_age !== null;
    setHasPartner(hasPartner);
    const processedData = processScenarioData({ ...scenarioData, includePartner: hasPartner });
    setInputData(processedData);
    setSelectedScenario(scenarioData);
  };

  const handleResetScenario = () => {
    setSelectedScenario(null);
    setInputData(null);
  };

  const [isExpanded, setIsExpanded] = useState(false);
  const [renderKey, setRenderKey] = useState(0);
  const [chartKey, setChartKey] = useState(0);
  const [activeTab, setActiveTab] = useState<'growth' | 'cashflow' | 'table' | 'success' | 'property'>('growth');
  const [activeInputTab, setActiveInputTab] = useState('personal');
  const [selectedHouseholdId, setSelectedHouseholdId] = useState<number | null>(null);

  const [showAdditionalData, setShowAdditionalData] = useState({
    show_savings: false,
    show_investment: false,
    show_individual_investments: false,
    show_kiwisaver: false,
    show_individual_kiwisavers: false,
    show_cashflow: false,
    show_monte_carlo: false,
    show_realistic_netwealth: false,
  });

  // Load saved checkbox states when scenario changes
  useEffect(() => {
    if (activeScenarioId) {
      // Check if we have saved checkbox states for this scenario
      const storageKey = `miscTabData_${activeScenarioId}`;
      const savedData = localStorage.getItem(storageKey);

      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);
          // Update the showAdditionalData state with saved values
          setShowAdditionalData({
            show_savings: parsedData.show_savings || false,
            show_investment: parsedData.show_investment || false,
            show_individual_investments: parsedData.show_individual_investments || false,
            show_kiwisaver: parsedData.show_kiwisaver || false,
            show_individual_kiwisavers: parsedData.show_individual_kiwisavers || false,
            show_cashflow: parsedData.show_cashflow || false,
            show_monte_carlo: parsedData.show_monte_carlo || false,
            show_realistic_netwealth: parsedData.show_realistic_netwealth || false,
          });
        } catch (e) {
          console.error('Error parsing saved checkbox states:', e);
        }
      }
    }
  }, [activeScenarioId]); // Only depend on activeScenarioId changes

  const handleMiscTabChange = (newData: {
    show_savings: any;
    show_investment: any;
    show_individual_investments: any;
    show_kiwisaver: any;
    show_individual_kiwisavers: any;
    show_cashflow: any;
    show_monte_carlo: any;
    show_realistic_netwealth: any;
  }) => {
    setShowAdditionalData({
      show_savings: newData.show_savings,
      show_investment: newData.show_investment,
      show_individual_investments: newData.show_individual_investments || false,
      show_kiwisaver: newData.show_kiwisaver,
      show_individual_kiwisavers: newData.show_individual_kiwisavers || false,
      show_cashflow: newData.show_cashflow,
      show_monte_carlo: newData.show_monte_carlo,
      show_realistic_netwealth: newData.show_realistic_netwealth,
    });
  };

  // Add a debounce function to prevent excessive recalculations
  const debounce = (func: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };

  // Debounced version of recalculating financial metrics
  const debouncedRecalculate = useCallback(
    debounce((updatedData: InputData) => {

      const result = calculateFinancialLife(updatedData as unknown as FinancialData);
      setAllMetrics(result.allMetrics);
      setChanceOfSuccess(result.chanceOfSuccess);
      setSuccessfulScenarios(result.successfulScenarios);
      setFailedScenarios(result.failedScenarios);
      setMinNetWealthAtAge(result.worstNetWealthAtAge);
      setMaxNetWealthAtAge(result.bestNetWealthAtAge);
      setAverageNetWealthAtAge(result.averageNetWealthAtAge);
    }, 0), // 0ms delay
    []
  );

  const handleMiscInputChange = (name: string, value: number) => {
    setInputData((prevData) => {
      if (prevData === null) return null;

      // Update the appropriate field based on the name
      const updatedData = {
        ...prevData,
        [name]: value,
      };

      // If we've updated any of the simulation parameters, schedule a recalculation
      if (name === 'inflation_rate' || name === 'num_simulations' || name === 'confidence_interval') {
        debouncedRecalculate(updatedData);
      }

      // Save to scenario-specific state if we have an active scenario
      if (activeScenarioId) {
        setScenarioInputData(prev => ({
          ...prev,
          [activeScenarioId]: updatedData
        }));
      }

      return updatedData;
    });
  };

  const handleMonteCarloChange = (name: string, value: number | boolean) => {
    setInputData((prevData) => {
      if (prevData === null) return null;

      const updatedData = {
        ...prevData,
        [name]: value,
      };

      // Save to scenario-specific state if we have an active scenario
      if (activeScenarioId) {
        setScenarioInputData(prev => ({
          ...prev,
          [activeScenarioId]: updatedData
        }));
      }

      return updatedData;
    });
  };

  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);

  const [isAnnotationMode, setIsAnnotationMode] = useState(false);
  const [showAnnotations, setShowAnnotations] = useState(true);

  const [annotations, setAnnotations] = useState<{
    netWealth: Array<{
      x: string;
      y: string;
      text: string;
      color: string;
      series: string;
    }>;
  }>({ netWealth: [] });

  const handleAnnotationUpdate = (chartType: 'netWealth', newAnnotations: Array<{
    x: string;
    y: string;
    text: string;
    color: string;
    series: string;
  }>) => {
    setAnnotations(prev => ({
      ...prev,
      [chartType]: newAnnotations
    }));
  };

  function regenerateSeed(): void {
    throw new Error('Function not implemented.');
  }

  // Handle overlay mode toggle with immediate loading of all scenarios
  const handleOverlayModeToggle = (value: boolean) => {
    setIsOverlayMode(value);

    // If turning on overlay mode, immediately add the active scenario to scenarioData
    if (value && activeScenarioId && allMetrics.length > 0) {
      updateScenarioData();
    }
  };

  const handleConfigureScenarios = () => {
    setIsPresentationModalOpen(true);
  };

  const handleScenariosUpdate = (householdId: number, scenarioIds: number[]) => {
    // Construct the new URL with updated scenario IDs
    const queryParams = new URLSearchParams();
    queryParams.append('household_id', householdId.toString());
    scenarioIds.forEach((id, index) => {
      queryParams.append(`scenarioId${index + 1}`, id.toString());
    });

    // Use router.replace to update the URL without adding to history
    window.history.replaceState(null, '', `/protected/presentation?${queryParams.toString()}`);

    // Force a re-render and re-fetch of data based on new URL params
    // This is a bit of a hack, but it forces the component to re-evaluate searchParams
    // and re-run the useEffect that loads scenarios.
    // A more robust solution might involve a context API or Redux for state management.
    window.location.reload();
  };

  return (
    <div className="flex min-h-screen overflow-hidden">
      <PresentationSidebar
        isCollapsed={isCollapsed}
        setIsCollapsed={setIsCollapsed}
        scenarios={availableScenarios}
        activeScenarioId={activeScenarioId}
        onScenarioChange={handleScenarioChange}
        isOverlayMode={isOverlayMode}
        setIsOverlayMode={handleOverlayModeToggle}
        onConfigureScenarios={handleConfigureScenarios}
      />

      <div className="flex flex-col items-center justify-center flex-grow p-4 overflow-hidden max-h-screen">
        {isLoading ? (
          <div>Loading...</div>
        ) : !selectedScenario ? (
          <div className="w-full max-w-md space-y-4">
            <h1 className="text-2xl font-bold mb-4">Select a Scenario to Visualise</h1>
            <HouseholdSelector onHouseholdSelect={setSelectedHouseholdId} />
            <ScenarioSelector householdId={selectedHouseholdId} onScenarioSelect={handleScenarioSelect} />
          </div>
        ) : (
          <>
            <div className="flex-grow overflow-hidden flex flex-col w-full h-full max-h-[calc(100vh-8px)]">
              <main className="flex-grow flex flex-col overflow-hidden h-full">
                {isOverlayMode ? (
                  // Overlay mode - show comparison of scenarios
                  <ResizableCardContainer
                    isTopCardExpanded={isExpanded}
                    containerHeight={100}
                    minCardHeight={30}
                    topCard={
                      <Card className="h-full flex flex-col overflow-hidden pt-5">
                        <CardHeader className="py-2 relative">
                          <CardTitle style={{ fontSize: '1rem' }} className="absolute left-4 top-1/2 transform -translate-y-1/2">
                            Scenario Comparison
                          </CardTitle>
                          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                            <button
                              onClick={() => {
                                setIsExpanded(!isExpanded);
                                setRenderKey(prev => prev + 1); // Force re-render when expanding/collapsing
                              }}
                              className="p-1 rounded-full hover:bg-gray-200 focus:outline-none"
                            >
                              <Maximize2 size={18} />
                            </button>
                          </div>
                        </CardHeader>
                        <CardContent className="flex-grow overflow-hidden transition-all duration-1000 pt-5">
                          <div className={`w-full h-full transition-opacity duration-1000 ${chartVisible ? 'opacity-100' : 'opacity-0'}`}>
                            <ChartContainer
                              config={chartConfig}
                              className="w-full h-full overflow-hidden"
                              key={`chart-container-overlay-${isExpanded ? 'expanded' : 'collapsed'}-${renderKey}-${chartKey}`}
                            >
                              <PresentationNetWealth
                                key={`presentation-netwealth-${chartKey}`}
                                scenarios={scenarioData}
                                isExpanded={isExpanded}
                                chartConfig={chartConfig}
                              />
                            </ChartContainer>
                          </div>
                        </CardContent>
                      </Card>
                    }
                    bottomCard={
                      <Card className="h-full overflow-hidden">
                        <CardContent className="p-4 h-full overflow-auto">
                          <KeyMetrics
                            scenarios={scenarioData.map(scenario => {
                              // Calculate metrics for each scenario
                              const inputData = scenarioInputData[scenario.id] as unknown as FinancialData;
                              const result = calculateFinancialLife(inputData);
                              return {
                                ...scenario,
                                chanceOfSuccess: result.chanceOfSuccess,
                                successfulScenarios: result.successfulScenarios,
                                failedScenarios: result.failedScenarios,
                                inputData: inputData
                              };
                            })}
                          />
                        </CardContent>
                      </Card>
                    }
                  />
                ) : (
                  // Normal mode - show single scenario
                  <ResizableCardContainer
                    isTopCardExpanded={isExpanded}
                    containerHeight={100}
                    minCardHeight={30}
                    topCard={
                      <Card className="h-full flex flex-col overflow-hidden">
                        <CardHeader className="py-2 relative">
                          <CardTitle style={{ fontSize: '1rem' }} className="absolute left-4 top-1/2 transform -translate-y-1/2">
                            {householdName} - {selectedScenario.scenario_name}
                          </CardTitle>
                          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                            {activeTab === 'growth'}
                            {activeTab === 'cashflow'}
                            {activeTab === 'property'}
                            {activeTab === 'table' && (
                              <button
                                onClick={() => {
                                  const worksheet = XLSX.utils.json_to_sheet(allMetrics);
                                  const workbook = XLSX.utils.book_new();
                                  XLSX.utils.book_append_sheet(workbook, worksheet, 'Financial Data');
                                  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
                                  const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                                  saveAs(data, 'financial_data.xlsx');
                                }}
                                className="flex items-center px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200 transition-colors"
                              >
                                <Download className="w-4 h-4 mr-1" />
                                Export to Excel
                              </button>
                            )}
                            <button
                              onClick={() => {
                                setIsExpanded(!isExpanded);
                                setRenderKey(prev => prev + 1); // Force re-render when expanding/collapsing
                              }}
                              className="p-1 rounded-full hover:bg-gray-200 focus:outline-none"
                            >
                              <Maximize2 size={18} />
                            </button>
                          </div>
                          <div className="flex justify-center w-full">
                            <div className="flex space-x-2">
                              <Button
                                variant={activeTab === 'growth' ? 'default' : 'outline'}
                                onClick={() => setActiveTab('growth')}
                                className="text-sm"
                              >
                                Growth
                              </Button>
                              <Button
                                variant={activeTab === 'cashflow' ? 'default' : 'outline'}
                                onClick={() => setActiveTab('cashflow')}
                                className="text-sm"
                              >
                                Cashflow
                              </Button>
                              <Button
                                variant={activeTab === 'property' ? 'default' : 'outline'}
                                onClick={() => setActiveTab('property')}
                                className="text-sm"
                              >
                                Property
                              </Button>
                              <Button
                                variant={activeTab === 'table' ? 'default' : 'outline'}
                                onClick={() => setActiveTab('table')}
                                className="text-sm"
                              >
                                Table
                              </Button>
                              <Button
                                variant={activeTab === 'success' ? 'default' : 'outline'}
                                onClick={() => setActiveTab('success')}
                                className="text-sm"
                              >
                                Chance of Success
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="flex-grow overflow-hidden transition-all duration-1000">
                          <div className={`w-full h-full transition-opacity duration-1000 ${chartVisible ? 'opacity-100' : 'opacity-0'}`}>
                            {activeTab === 'success' ? (
                              <MonteCarloResults
                                key={`monte-carlo-${chartKey}`}
                                chanceOfSuccess={chanceOfSuccess}
                                successfulScenarios={successfulScenarios}
                                failedScenarios={failedScenarios}
                                dataReady={dataReady}
                              />
                            ) : (
                              <ChartContainer
                                config={chartConfig}
                                className="w-full h-full overflow-hidden"
                                key={`chart-container-${isExpanded ? 'expanded' : 'collapsed'}-${renderKey}-${chartKey}`}
                              >
                              {activeTab === 'growth' ? (
                                <NetWealth
                                  key={`netwealth-${chartKey}`}
                                  allMetrics={allMetrics}
                                  showAdditionalData={showAdditionalData}
                                  inputData={inputData}
                                  minNetWealthAtAge={minNetWealthAtAge}
                                  maxNetWealthAtAge={maxNetWealthAtAge}
                                  averageNetWealthAtAge={averageNetWealthAtAge}
                                  isExpanded={isExpanded}
                                  chartConfig={chartConfig}
                                  annotations={annotations.netWealth}
                                  onAnnotationsChange={(newAnnotations) => handleAnnotationUpdate('netWealth', newAnnotations)}
                                  isAnnotationMode={isAnnotationMode}
                                  showAnnotations={showAnnotations}
                                  onAnnotationComplete={() => setIsAnnotationMode(false)}
                                  mainName={mainName}
                                  dataReady={dataReady}
                                />
                              ) : activeTab === 'cashflow' ? (
                                inputData?.show_cashflow_breakdown ? (
                                  <div className="flex w-full h-full">
                                    <div className="w-2/3 h-full">
                                      <Cashflow
                                        key={`cashflow-breakdown-${chartKey}`}
                                        allMetrics={allMetrics}
                                        isExpanded={isExpanded}
                                        chartConfig={chartConfig}
                                        onAgeHover={setSelectedAge}
                                        inputData={inputData}
                                        onShowCashflowBreakdownChange={(showCashflowBreakdown) => {
                                          setInputData(prev => prev ? {
                                            ...prev,
                                            show_cashflow_breakdown: showCashflowBreakdown
                                          } : prev);
                                        }}
                                      />
                                    </div>
                                    <div className="w-1/3 h-full pl-4 border-l flex flex-col">
                                      <div className="h-1/2 pb-2">
                                        <IncomeBreakdown
                                          allMetrics={allMetrics}
                                          selectedAge={selectedAge}
                                          inputData={inputData}
                                          mainName={mainName}
                                          partnerName={partnerName}
                                        />
                                      </div>
                                      <div className="h-1/2 pt-2 border-t">
                                        <ExpenseBreakdown
                                          allMetrics={allMetrics}
                                          selectedAge={selectedAge}
                                          inputData={inputData}
                                          mainName={mainName}
                                          partnerName={partnerName}
                                        />
                                      </div>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="w-full h-full">
                                    <Cashflow
                                      key={`cashflow-${chartKey}`}
                                      allMetrics={allMetrics}
                                      isExpanded={isExpanded}
                                      chartConfig={chartConfig}
                                      onAgeHover={setSelectedAge}
                                      inputData={inputData}
                                      onShowCashflowBreakdownChange={(showCashflowBreakdown) => {
                                        setInputData(prev => prev ? {
                                          ...prev,
                                          show_cashflow_breakdown: showCashflowBreakdown
                                        } : prev);
                                      }}
                                    />
                                  </div>
                                )
                              ) : activeTab === 'property' ? (
                                <Property
                                  key={`property-${chartKey}`}
                                  allMetrics={allMetrics}
                                  inputData={inputData}
                                  isExpanded={isExpanded}
                                  chartConfig={chartConfig}
                                  onShowRepaymentsChange={(showRepayments) => {
                                    setInputData(prev => prev ? {
                                      ...prev,
                                      show_repayments: showRepayments
                                    } : prev);
                                  }}
                                  dataReady={dataReady}
                                />
                              ) : (
                                <div className="w-full h-full overflow-auto">
                                  <ModellingTableWithTabs
                                    inputData={inputData}
                                    mainName={mainName}
                                    partnerName={partnerName}
                                  />
                                </div>
                              )}
                            </ChartContainer>
                          )}
                          </div>
                        </CardContent>
                        <CardFooter className="py-2">
                          <div className="flex w-full items-start gap-2 text-sm">
                            {/* Add any footer content here if needed */}
                          </div>
                        </CardFooter>
                      </Card>
                    }
                    bottomCard={
                      <Card className="h-full overflow-hidden">
                        <CardContent className="p-0 h-full overflow-hidden">
                          <Tabs
                            value={activeInputTab}
                            onValueChange={setActiveInputTab}
                            className="w-full h-full flex flex-col overflow-hidden"
                          >
                            <CardHeader className="pb-2 tabs-header">
                              <TabsList className="grid w-full grid-cols-9">
                                <TabsTrigger value="personal">Personal</TabsTrigger>
                                <TabsTrigger value="income">Income</TabsTrigger>
                                <TabsTrigger value="expenditure">Expenditure</TabsTrigger>
                                <TabsTrigger value="savings">Savings</TabsTrigger>
                                <TabsTrigger value="investment">Investment</TabsTrigger>
                                <TabsTrigger value="kiwiSaver">KiwiSaver</TabsTrigger>
                                <TabsTrigger value="property">Property</TabsTrigger>
                                <TabsTrigger value="whatIf">What If</TabsTrigger>
                                <TabsTrigger value="misc">Misc</TabsTrigger>
                              </TabsList>
                            </CardHeader>
                            <div className="flex-grow overflow-auto px-6 py-4 tabs-content rounded-lg">
                              <TabsContent value="personal">
                                {inputData && (
                                  <PersonalTab
                                    inputData={inputData}
                                    setInputData={updateInputData}
                                    hasPartner={hasPartner}
                                    setHasPartner={setHasPartner}
                                    mainName={mainName}
                                    partnerName={partnerName}
                                  />
                                )}
                              </TabsContent>
                              <TabsContent value="income">
                                {inputData && (
                                  <IncomeTab
                                    inputData={inputData}
                                    setInputData={updateInputData}
                                    hasPartner={hasPartner}
                                    mainName={mainName}
                                    partnerName={partnerName}
                                  />
                                )}
                              </TabsContent>
                              <TabsContent value="expenditure">
                                {inputData && (
                                  <ExpenseTab inputData={inputData} setInputData={updateInputData} />
                                )}
                              </TabsContent>
                              <TabsContent value="savings">
                                {inputData && (
                                  <SavingsTab inputData={inputData} setInputData={updateInputData} />
                                )}
                              </TabsContent>
                              <TabsContent value="investment">
                                {inputData && (
                                  <InvestmentTab inputData={inputData} setInputData={updateInputData} />
                                )}
                              </TabsContent>
                              <TabsContent value="kiwiSaver">
                                {inputData && (
                                  <KiwiSaverTab
                                    inputData={inputData}
                                    setInputData={updateInputData}
                                    hasPartner={hasPartner}
                                    mainName={mainName}
                                    partnerName={partnerName}
                                  />
                                )}
                              </TabsContent>
                              <TabsContent value="property">
                                {inputData && (
                                  <PropertyTab
                                    inputData={inputData}
                                    setInputData={updateInputData}
                                    allMetrics={allMetrics}
                                  />
                                )}
                              </TabsContent>
                              <TabsContent value="whatIf">
                                {inputData && (
                                  <WhatIfTab
                                    inputData={inputData}
                                    setInputData={updateInputData}
                                    mainName={mainName}
                                    partnerName={partnerName}
                                  />
                                )}
                              </TabsContent>
                              <TabsContent value="misc">
                                {selectedScenario && inputData && inputData.household_id && (
                                  <MiscTab
                                    onCheckboxChange={handleMiscTabChange}
                                    onInputChange={handleMiscInputChange}
                                    onDataChange={handleMonteCarloChange}
                                    inputData={inputData}
                                    currentScenarioId={activeScenarioId || 0}
                                    householdId={inputData.household_id}
                                    householdName={householdName}
                                    scenarioName={selectedScenario?.scenario_name || ''}
                                    mainMemberName={mainName}
                                    onRegenerateSeed={regenerateSeed}
                                    onAnnotationModeChange={setIsAnnotationMode}
                                    onShowAnnotationsChange={setShowAnnotations}
                                    isAnnotationMode={isAnnotationMode}
                                  />
                                )}
                              </TabsContent>
                            </div>
                          </Tabs>
                        </CardContent>
                      </Card>
                    }
                  />
                )}
              </main>
            </div>
            <PresentationModal
              isOpen={isPresentationModalOpen}
              onClose={() => setIsPresentationModalOpen(false)}
              preselectedHousehold={selectedScenario ? { id: selectedScenario.household_id, householdName: selectedScenario.household_name } : undefined}
              preselectedScenarios={availableScenarios.map(s => s.id)}
              onScenariosUpdate={handleScenariosUpdate}
            />
          </>
        )}
      </div>
    </div>
  );
}
