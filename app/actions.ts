"use server";

import { encodedRedirect } from "@/utils/utils";
import { createClient } from "@/utils/supabase/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { validatePassword } from "@/utils/password-validation";
import { sendAdminNotification } from "./actions/notifications";
import { createOrganizationAdmin, associateUserWithOrganizationAdmin, updateInvitationStatusAdmin } from "./actions/admin";

export const signUpAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const confirmPassword = formData.get("confirmPassword")?.toString();
  const firstName = formData.get("firstName")?.toString();
  const lastName = formData.get("lastName")?.toString();
  const phone = formData.get("phone")?.toString();
  const organizationName = formData.get("organizationName")?.toString();
  const invitationToken = formData.get("invitationToken")?.toString();

  const supabase = createClient();
  const origin = headers().get("origin");

  // Validate all required fields
  if (!email || !password || !confirmPassword || !firstName || !lastName || !organizationName || !phone) {
    console.error("Missing required fields");
    return encodedRedirect("error", "/sign-up", "All required fields must be filled");
  }

  // Validate password match
  if (password !== confirmPassword) {
    console.error("Passwords do not match");
    return encodedRedirect("error", "/sign-up", "Passwords do not match");
  }

  // Server-side password strength validation
  const passwordValidation = validatePassword(password);
  if (!passwordValidation.isValid) {
    console.error("Password validation failed:", passwordValidation.errors);
    return encodedRedirect("error", "/sign-up", `Password requirements not met: ${passwordValidation.errors[0]}`);
  }

  // Sign up the user
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/auth/callback`,
      data: {
        first_name: firstName,
        last_name: lastName,
        phone: phone || null,
        full_name: `${firstName} ${lastName}`,
      },
    },
  });

  if (authError) {
    console.error("Supabase signup error:", authError.code, authError.message);
    return encodedRedirect("error", "/sign-up", authError.message);
  }

  const user = authData.user;

  if (!user) {
    return encodedRedirect(
      "error",
      "/sign-up",
      "An error occurred during sign-up. Please try again."
    );
  }

  try {
    // Note: Profile will be automatically created by the database trigger
    // when the user confirms their email. We'll handle organization assignment here.

    // Handle organization assignment
    if (invitationToken) {
      console.log(`Processing invitation token: ${invitationToken}`);

      // If signing up via invitation, associate with existing organization
      const { data: invitation, error: invitationError } = await supabase
        .from('organization_invitations')
        .select('id, organization_id, token, role')
        .eq('token', invitationToken)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .single();

      if (invitationError) {
        console.error("Error querying invitation:", invitationError);
        console.log("Will create a new organization instead");
        // Continue with sign-up but create new organization using admin privileges
        const orgResult = await createOrganizationAdmin(organizationName, user.id);
        if (!orgResult.success) {
          console.error("Error creating organization with admin privileges:", orgResult.error);
        } else {
          console.log("Organization created successfully with admin privileges, ID:", orgResult.organizationId);
        }
      } else if (!invitation) {
        console.error("No invitation found for token:", invitationToken);
        console.log("Will create a new organization instead");
        // Continue with sign-up but create new organization using admin privileges
        const orgResult = await createOrganizationAdmin(organizationName, user.id);
        if (!orgResult.success) {
          console.error("Error creating organization with admin privileges:", orgResult.error);
        } else {
          console.log("Organization created successfully with admin privileges, ID:", orgResult.organizationId);
        }
      } else {
        console.log(`Found invitation: ${invitation.id} for organization: ${invitation.organization_id}`);

        // Get the role from the invitation or default to 'readonly'
        const role = invitation.role || 'readonly';
        console.log(`Using role from invitation: ${role}`);

        // Associate user with existing organization using admin privileges
        const associateResult = await associateUserWithOrganizationAdmin(user.id, invitation.organization_id, role);
        if (!associateResult.success) {
          console.error("Error associating user with organization:", associateResult.error);
        } else {
          console.log(`User associated with organization successfully with role: ${role}`);

          // Update invitation status using admin privileges
          const updateResult = await updateInvitationStatusAdmin(invitationToken, 'accepted');
          if (!updateResult.success) {
            console.error("Error updating invitation status:", updateResult.error);
          } else {
            console.log("Invitation status updated to 'accepted'");
          }
        }
      }
    } else {
      // Create new organization for the user using admin privileges
      const orgResult = await createOrganizationAdmin(organizationName, user.id);
      if (!orgResult.success) {
        console.error("Error creating organization with admin privileges:", orgResult.error);
      } else {
        console.log("Organization created successfully with admin privileges, ID:", orgResult.organizationId);
      }
    }

    // Send notification to admin
    await sendAdminNotification({
      email,
      firstName,
      lastName,
      organizationName,
      phone: phone || 'Not provided'
    });

  } catch (error) {
    console.error("Error handling organization:", error);
    // Continue with sign-up process even if organization handling fails
  }

  return encodedRedirect(
    "success",
    "/sign-up",
    "Thanks for signing up! Please check your email for a verification link.",
  );
};

export const signInAction = async (formData: FormData) => {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const supabase = createClient();

  console.log('Sign-in action: Attempting sign-in for email:', email);

  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    console.error('Sign-in action: Authentication failed:', error.message);
    return encodedRedirect("error", "/sign-in", error.message);
  }

  console.log('Sign-in action: Authentication successful for user:', data.user?.id);

  // Check if MFA is required
  const { data: aalData, error: aalError } = await supabase.auth.mfa.getAuthenticatorAssuranceLevel();

  if (aalError) {
    console.error("Sign-in action: Error checking MFA status:", aalError);
    return redirect("/protected");
  }

  // If user has MFA enrolled but not verified for this session, redirect to MFA challenge
  if (aalData.nextLevel === 'aal2' && aalData.currentLevel === 'aal1') {
    console.log('Sign-in action: MFA required, redirecting to challenge');
    return redirect("/mfa-challenge");
  }

  console.log('Sign-in action: Redirecting to protected area');
  return redirect("/protected");
};

export const forgotPasswordAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const supabase = createClient();
  const callbackUrl = formData.get("callbackUrl")?.toString();

  if (!email) {
    return encodedRedirect("error", "/forgot-password", "Email is required");
  }

  // Use the site URL directly without any additional configuration
  // Let Supabase use the default site URL from the Supabase dashboard
  const { error } = await supabase.auth.resetPasswordForEmail(email);

  if (error) {
    console.error(error.message);
    return encodedRedirect(
      "error",
      "/forgot-password",
      "Could not reset password",
    );
  }

  if (callbackUrl) {
    return redirect(callbackUrl);
  }

  return encodedRedirect(
    "success",
    "/forgot-password",
    "Check your email for a link to reset your password.",
  );
};

export const resetPasswordAction = async (formData: FormData) => {
  const supabase = createClient();

  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    return encodedRedirect(
      "error",
      "/reset-password",
      "Password and confirm password are required",
    );
  }

  if (password !== confirmPassword) {
    return encodedRedirect(
      "error",
      "/reset-password",
      "Passwords do not match",
    );
  }

  const { error } = await supabase.auth.updateUser({
    password: password,
  });

  if (error) {
    return encodedRedirect(
      "error",
      "/reset-password",
      "Password update failed: " + error.message,
    );
  }

  // Redirect to sign-in page with success message after password reset
  return encodedRedirect("success", "/sign-in", "Password has been updated successfully. Please sign in with your new password.");
};

export async function signOutAction() {
  const supabase = createClient();
  await supabase.auth.signOut();
  redirect("/sign-in");
};

export async function createHousehold(formData: { householdName: string; }) {
  const supabase = createClient();

  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    throw new Error("User not authenticated");
  }

  const { data, error } = await supabase
    .from('households')
    .insert({
      householdName: formData.householdName,
      user_id: user.id
    })
    .select()
    .single();

  if (error) {
    console.error('Error inserting household:', error);
    throw error;
  }

  return data;
}

// MFA related actions
export const checkMFAStatusAction = async () => {
  const supabase = createClient();

  // Check if user is authenticated
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return { error: "Not authenticated" };
  }

  // Check MFA status
  const { data: aalData, error: aalError } = await supabase.auth.mfa.getAuthenticatorAssuranceLevel();

  if (aalError) {
    return { error: aalError.message };
  }

  // Get MFA factors
  const { data: factorsData, error: factorsError } = await supabase.auth.mfa.listFactors();

  if (factorsError) {
    return { error: factorsError.message };
  }

  // Check if any factors are verified
  const allFactors = [...(factorsData.totp || []), ...(factorsData.phone || [])];
  const hasVerifiedFactors = allFactors.some(factor => factor.status === 'verified');

  return {
    currentLevel: aalData.currentLevel,
    nextLevel: aalData.nextLevel,
    hasMFA: hasVerifiedFactors,
    factors: allFactors
  };
};

export const verifyMFAAction = async (formData: FormData) => {
  const factorId = formData.get("factorId") as string;
  const challengeId = formData.get("challengeId") as string;
  const code = formData.get("code") as string;

  if (!factorId || !challengeId || !code) {
    return encodedRedirect("error", "/mfa-challenge", "Missing required fields");
  }

  const supabase = createClient();

  const { error } = await supabase.auth.mfa.verify({
    factorId,
    challengeId,
    code,
  });

  if (error) {
    return encodedRedirect("error", "/mfa-challenge", error.message);
  }

  return redirect("/protected");
};

export const unenrollMFAAction = async (formData: FormData) => {
  const factorId = formData.get("factorId") as string;
  const cleanupAll = formData.get("cleanupAll") === "true";
  const returnPath = formData.get("returnPath") as string || "/protected/profile";

  const supabase = createClient();

  if (cleanupAll) {
    // Clean up all factors (useful for troubleshooting)
    try {
      console.log('Cleaning up all MFA factors');

      // Get all factors
      const { data, error: listError } = await supabase.auth.mfa.listFactors();

      if (listError) {
        console.error('Error listing factors:', listError);
        return encodedRedirect("error", returnPath, listError.message);
      }

      // Combine all factor types
      const allFactors = [...(data.totp || []), ...(data.phone || [])];
      console.log(`Found ${allFactors.length} factors to clean up`);

      // Unenroll each factor
      for (const factor of allFactors) {
        try {
          console.log(`Unenrolling factor: ${factor.id}`);
          await supabase.auth.mfa.unenroll({ factorId: factor.id });
        } catch (unenrollError) {
          console.error(`Error unenrolling factor ${factor.id}:`, unenrollError);
          // Continue with other factors even if one fails
        }
      }

      // Update the profile to set mfa_enabled to false
      try {
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (!userError && user) {
          await supabase
            .from('profiles')
            .update({ mfa_enabled: false })
            .eq('user_id', user.id);
        }
      } catch (profileError) {
        console.error('Error updating profile:', profileError);
      }

      return encodedRedirect("success", returnPath, "All MFA factors removed successfully");
    } catch (err: any) {
      console.error('Error in cleanup:', err);
      return encodedRedirect("error", returnPath, err.message || "Failed to clean up MFA factors");
    }
  }

  // Regular single factor unenrollment
  if (!factorId) {
    return encodedRedirect("error", returnPath, "Missing factor ID");
  }

  console.log(`Unenrolling single factor: ${factorId}`);
  const { error } = await supabase.auth.mfa.unenroll({
    factorId,
  });

  if (error) {
    console.error('Error unenrolling factor:', error);
    return encodedRedirect("error", returnPath, error.message);
  }

  return encodedRedirect("success", returnPath, "MFA factor removed successfully");
};

export const updateEmailAction = async (formData: FormData) => {
  const currentPassword = formData.get("currentPassword") as string;
  const newEmail = formData.get("newEmail") as string;
  const returnPath = formData.get("returnPath") as string || "/protected/profile";

  const supabase = createClient();

  // Validate inputs
  if (!currentPassword || !newEmail) {
    return encodedRedirect(
      "error",
      returnPath,
      "Password and new email are required"
    );
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(newEmail)) {
    return encodedRedirect(
      "error",
      returnPath,
      "Please enter a valid email address"
    );
  }

  // Get current user
  const { data: { user }, error: getUserError } = await supabase.auth.getUser();

  if (getUserError || !user) {
    return encodedRedirect("error", returnPath, "User not authenticated");
  }

  try {
    // First verify the password is correct by attempting a password update with the same password
    // This is a workaround to verify the password without changing anything
    const { error: passwordCheckError } = await supabase.auth.updateUser(
      { password: currentPassword },
      { password: currentPassword } as any
    );

    if (passwordCheckError) {
      if (passwordCheckError.message.includes('password')) {
        return encodedRedirect("error", returnPath, "The password you entered is incorrect");
      }
      return encodedRedirect("error", returnPath, passwordCheckError.message);
    }

    // Now try to update the email
    const { error } = await supabase.auth.updateUser(
      { email: newEmail },
      { password: currentPassword } as any
    );

    if (error) {
      console.error('Error updating email:', error);

      // Handle specific error types
      if (error.message.includes('sending email')) {
        // If there's an issue with the email service, we'll still update the profile in the database
        // This way the UI can show the pending change even if the email verification fails
        try {
          // Update the email in the profiles table as a fallback
          // Note: This won't change the auth email until verified, but provides UI feedback
          const { error: profileUpdateError } = await supabase
            .from('profiles')
            .update({ pending_email: newEmail })
            .eq('user_id', user.id);

          if (profileUpdateError) {
            console.error('Error updating profile with pending email:', profileUpdateError);
          }

          return encodedRedirect(
            "warning",
            returnPath,
            "There was an issue sending the verification email. Your password is correct, but we could not send the email. Please try again later."
          );
        } catch (profileErr) {
          console.error('Error in profile fallback update:', profileErr);
        }
      }

      return encodedRedirect("error", returnPath, error.message);
    }

    // If successful, return success message
    return encodedRedirect(
      "success",
      returnPath,
      "Email update initiated. Please check your new email for a confirmation link."
    );
  } catch (err: any) {
    console.error('Unexpected error updating email:', err);
    return encodedRedirect("error", returnPath, err.message || "An unexpected error occurred");
  }
};

export const registerDeviceAction = async (formData: FormData) => {
  const deviceName = formData.get("deviceName") as string;
  const deviceId = formData.get("deviceId") as string;
  const userAgent = formData.get("userAgent") as string;
  const returnPath = formData.get("returnPath") as string || "/protected/profile";

  const supabase = createClient();

  console.log('registerDeviceAction called with:', {
    deviceId: deviceId?.substring(0, 10) + '...',
    deviceName,
    returnPath
  });

  // Validate inputs
  if (!deviceName || !deviceId) {
    console.error('Missing device information');
    return encodedRedirect(
      "error",
      returnPath,
      "Device information is required"
    );
  }

  // Get current user
  const { data: { user }, error: getUserError } = await supabase.auth.getUser();

  if (getUserError || !user) {
    console.error('User not authenticated');
    return encodedRedirect("error", returnPath, "User not authenticated");
  }

  console.log('Processing device registration for user:', user.id);

  // Check if this device is already registered
  const { data: existingDevice, error: deviceError } = await supabase
    .from('user_devices')
    .select('*')
    .eq('user_id', user.id)
    .eq('device_id', deviceId)
    .maybeSingle();

  if (deviceError) {
    console.error('Error checking device:', deviceError);
    return encodedRedirect("error", returnPath, deviceError.message);
  }

  // If device already exists, update it
  if (existingDevice) {
    console.log('Device already exists, updating:', existingDevice.id);

    const { error: updateError } = await supabase
      .from('user_devices')
      .update({
        device_name: deviceName,
        user_agent: userAgent,
        last_active: new Date().toISOString(),
        is_current: true
      })
      .eq('id', existingDevice.id);

    if (updateError) {
      console.error('Error updating device:', updateError);
      return encodedRedirect("error", returnPath, updateError.message);
    }

    // Set all other devices as not current
    const { error: updateOthersError } = await supabase
      .from('user_devices')
      .update({ is_current: false })
      .eq('user_id', user.id)
      .neq('id', existingDevice.id);

    if (updateOthersError) {
      console.error('Error updating other devices:', updateOthersError);
    }

    return { success: true, message: "Device updated successfully" };
  }

  // Check if user already has 3 devices
  const { data: existingDevices, error: countError } = await supabase
    .from('user_devices')
    .select('id')
    .eq('user_id', user.id);

  if (countError) {
    console.error('Error counting devices:', countError);
    return encodedRedirect("error", returnPath, countError.message);
  }

  console.log('User has', existingDevices?.length || 0, 'existing devices');

  // If user has 3 devices and this is a new one, return error
  if (existingDevices && existingDevices.length >= 3) {
    console.error('User has reached maximum number of devices');
    return encodedRedirect(
      "error",
      returnPath,
      "You have reached the maximum number of devices (3). Please remove a device before adding a new one."
    );
  }

  // Register new device
  console.log('Registering new device for user:', user.id);

  const { data: newDevice, error: insertError } = await supabase
    .from('user_devices')
    .insert({
      user_id: user.id,
      device_name: deviceName,
      device_id: deviceId,
      user_agent: userAgent,
      is_current: true
    })
    .select()
    .single();

  if (insertError) {
    console.error('Error registering device:', insertError);
    return encodedRedirect("error", returnPath, insertError.message);
  }

  console.log('New device registered:', newDevice);

  // Set all other devices as not current
  const { error: updateOthersError } = await supabase
    .from('user_devices')
    .update({ is_current: false })
    .eq('user_id', user.id)
    .neq('id', newDevice.id);

  if (updateOthersError) {
    console.error('Error updating other devices:', updateOthersError);
  }

  return { success: true, message: "Device registered successfully" };
};

export const removeDeviceAction = async (formData: FormData) => {
  const deviceId = formData.get("deviceId") as string;
  const returnPath = formData.get("returnPath") as string || "/protected/profile";

  const supabase = createClient();

  console.log('removeDeviceAction called with:', {
    deviceId: deviceId?.substring(0, 10) + '...',
    returnPath
  });

  // Validate inputs
  if (!deviceId) {
    console.error('Missing device ID');
    return encodedRedirect(
      "error",
      returnPath,
      "Device ID is required"
    );
  }

  // Get current user
  const { data: { user }, error: getUserError } = await supabase.auth.getUser();

  if (getUserError || !user) {
    console.error('User not authenticated');
    return encodedRedirect("error", returnPath, "User not authenticated");
  }

  console.log('Processing device removal for user:', user.id);

  // Get the device to check if it exists and if it's current
  const { data: deviceToRemove, error: getDeviceError } = await supabase
    .from('user_devices')
    .select('*')
    .eq('user_id', user.id)
    .eq('device_id', deviceId)
    .maybeSingle();

  if (getDeviceError) {
    console.error('Error getting device:', getDeviceError);
    return encodedRedirect("error", returnPath, getDeviceError.message);
  }

  if (!deviceToRemove) {
    console.error('Device not found');
    return encodedRedirect("error", returnPath, "Device not found");
  }

  console.log('Found device to remove:', deviceToRemove);

  // Delete the device
  const { error: deleteError } = await supabase
    .from('user_devices')
    .delete()
    .eq('id', deviceToRemove.id);

  if (deleteError) {
    console.error('Error removing device:', deleteError);
    return encodedRedirect("error", returnPath, deleteError.message);
  }

  console.log('Device removed successfully');

  // If this was the current device, sign out
  const isCurrent = formData.get("isCurrent") === "true" || deviceToRemove.is_current;
  if (isCurrent) {
    console.log('Signing out current device');
    await supabase.auth.signOut();
    return redirect("/sign-in");
  }

  return encodedRedirect("success", returnPath, "Device removed successfully");
};

export const updateDeviceNameAction = async (formData: FormData) => {
  const deviceId = formData.get("deviceId") as string;
  const deviceName = formData.get("deviceName") as string;
  const returnPath = formData.get("returnPath") as string || "/protected/profile";

  const supabase = createClient();

  // Validate inputs
  if (!deviceId || !deviceName) {
    return encodedRedirect(
      "error",
      returnPath,
      "Device ID and name are required"
    );
  }

  // Get current user
  const { data: { user }, error: getUserError } = await supabase.auth.getUser();

  if (getUserError || !user) {
    return encodedRedirect("error", returnPath, "User not authenticated");
  }

  // Update the device name
  const { error: updateError } = await supabase
    .from('user_devices')
    .update({ device_name: deviceName })
    .eq('user_id', user.id)
    .eq('device_id', deviceId);

  if (updateError) {
    console.error('Error updating device name:', updateError);
    return encodedRedirect("error", returnPath, updateError.message);
  }

  return encodedRedirect("success", returnPath, "Device name updated successfully");
};

export const updateProfileAction = async (formData: FormData) => {
  const name = formData.get("name") as string;
  const returnPath = formData.get("returnPath") as string || "/protected/profile";

  const supabase = createClient();

  // Validate inputs
  if (!name || name.trim() === '') {
    return encodedRedirect(
      "error",
      returnPath,
      "Name is required"
    );
  }

  // Get current user
  const { data: { user }, error: getUserError } = await supabase.auth.getUser();

  if (getUserError || !user) {
    return encodedRedirect("error", returnPath, "User not authenticated");
  }

  // Update profile in the profiles table
  const { error } = await supabase
    .from('profiles')
    .update({ name: name.trim() })
    .eq('user_id', user.id);

  if (error) {
    console.error('Error updating profile:', error);
    return encodedRedirect("error", returnPath, error.message);
  }

  return encodedRedirect("success", returnPath, "Profile updated successfully");
};

export const changePasswordAction = async (formData: FormData) => {
  const currentPassword = formData.get("currentPassword") as string;
  const newPassword = formData.get("newPassword") as string;
  const confirmPassword = formData.get("confirmPassword") as string;
  const returnPath = formData.get("returnPath") as string || "/protected/profile";

  const supabase = createClient();

  // Validate inputs
  if (!currentPassword || !newPassword || !confirmPassword) {
    return encodedRedirect(
      "error",
      returnPath,
      "All password fields are required"
    );
  }

  if (newPassword !== confirmPassword) {
    return encodedRedirect(
      "error",
      returnPath,
      "New passwords do not match"
    );
  }

  // First verify the current password by attempting to sign in
  const { data: { user }, error: getUserError } = await supabase.auth.getUser();

  if (getUserError || !user) {
    return encodedRedirect("error", returnPath, "User not authenticated");
  }

  // Attempt to update the password
  const { error } = await supabase.auth.updateUser(
    { password: newPassword },
    // Use type assertion to include password in options
    {
      // This is the recommended way to update password when user is logged in
      // It requires the current password for verification
      password: currentPassword
    } as any
  );

  if (error) {
    console.error('Error updating password:', error);
    return encodedRedirect("error", returnPath, error.message);
  }

  return encodedRedirect("success", returnPath, "Password updated successfully");
};

export const updateMfaEnabledAction = async (formData: FormData) => {
  const enabled = formData.get("enabled") === "true";
  const returnPath = formData.get("returnPath") as string || "/protected/profile";

  const supabase = createClient();

  try {
    // Get user data
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('User not authenticated');
      return encodedRedirect("error", returnPath, "User not authenticated");
    }

    // Update profile
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ mfa_enabled: enabled })
      .eq('user_id', user.id);

    if (updateError) {
      console.error('Error updating MFA setting:', updateError);
      return encodedRedirect("error", returnPath, updateError.message);
    }

    // If disabling MFA, also unenroll all factors
    if (!enabled) {
      // Get all factors
      const { data, error: listError } = await supabase.auth.mfa.listFactors();

      if (listError) {
        console.error('Error listing factors:', listError);
        return encodedRedirect("error", returnPath, listError.message);
      }

      // Combine all factor types
      const allFactors = [...(data.totp || []), ...(data.phone || [])];
      const verifiedFactors = allFactors.filter(factor => factor.status === 'verified');

      // Unenroll each verified factor
      for (const factor of verifiedFactors) {
        try {
          console.log(`Unenrolling factor: ${factor.id}`);
          await supabase.auth.mfa.unenroll({ factorId: factor.id });
        } catch (unenrollError) {
          console.error(`Error unenrolling factor ${factor.id}:`, unenrollError);
        }
      }
    }

    return encodedRedirect("success", returnPath, `Two-factor authentication ${enabled ? 'enabled' : 'disabled'} successfully`);
  } catch (err: any) {
    console.error('Error updating MFA setting:', err);
    return encodedRedirect("error", returnPath, err.message || 'Failed to update MFA setting');
  }
};
