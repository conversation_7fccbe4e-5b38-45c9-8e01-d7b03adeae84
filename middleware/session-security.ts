/**
 * Server-Side Session Security Middleware
 * 
 * Provides server-side session validation, security monitoring,
 * and cross-device session management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export interface SessionSecurityContext {
  isValid: boolean;
  userId: string | null;
  sessionId: string | null;
  deviceFingerprint: string;
  ipAddress: string;
  userAgent: string;
  securityFlags: {
    suspiciousActivity: boolean;
    multipleDevices: boolean;
    locationChange: boolean;
    rateLimitExceeded: boolean;
  };
  lastActivity: Date | null;
  expiresAt: Date | null;
}

/**
 * Session security configuration
 */
export const SESSION_SECURITY_CONFIG = {
  // Maximum requests per minute per session
  MAX_REQUESTS_PER_MINUTE: 60,
  // Maximum concurrent sessions per user
  MAX_CONCURRENT_SESSIONS: 3,
  // Session inactivity timeout (8 hours)
  INACTIVITY_TIMEOUT: 8 * 60 * 60 * 1000,
  // Maximum session duration (24 hours)
  MAX_SESSION_DURATION: 24 * 60 * 60 * 1000,
  // Suspicious activity threshold
  SUSPICIOUS_ACTIVITY_THRESHOLD: 100 // requests per minute
};

/**
 * In-memory session tracking (in production, use Redis or database)
 */
const sessionTracker = new Map<string, {
  userId: string;
  deviceFingerprint: string;
  ipAddress: string;
  requestCount: number;
  lastRequest: number;
  createdAt: number;
  lastActivity: number;
}>();

/**
 * Rate limiting tracker
 */
const rateLimitTracker = new Map<string, {
  count: number;
  resetTime: number;
}>();

/**
 * Generate device fingerprint from request headers
 */
function generateDeviceFingerprint(request: NextRequest): string {
  const userAgent = request.headers.get('user-agent') || '';
  const acceptLanguage = request.headers.get('accept-language') || '';
  const acceptEncoding = request.headers.get('accept-encoding') || '';
  
  const fingerprint = `${userAgent}-${acceptLanguage}-${acceptEncoding}`;
  
  // Create a simple hash
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  
  return `fp_${Math.abs(hash).toString(36)}`;
}

/**
 * Get client IP address
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

/**
 * Check rate limiting
 */
function checkRateLimit(identifier: string): boolean {
  const now = Date.now();
  const windowStart = now - 60000; // 1 minute window
  
  const current = rateLimitTracker.get(identifier) || { count: 0, resetTime: now + 60000 };
  
  if (current.resetTime < now) {
    current.count = 0;
    current.resetTime = now + 60000;
  }
  
  current.count++;
  rateLimitTracker.set(identifier, current);
  
  return current.count <= SESSION_SECURITY_CONFIG.MAX_REQUESTS_PER_MINUTE;
}

/**
 * Check for suspicious activity
 */
function checkSuspiciousActivity(sessionId: string, deviceFingerprint: string): boolean {
  const session = sessionTracker.get(sessionId);
  if (!session) return false;
  
  const now = Date.now();
  const timeSinceLastRequest = now - session.lastRequest;
  
  // Check for rapid requests (potential bot activity)
  if (timeSinceLastRequest < 100) { // Less than 100ms between requests
    session.requestCount++;
    
    // If more than 100 requests per minute, flag as suspicious
    if (session.requestCount > SESSION_SECURITY_CONFIG.SUSPICIOUS_ACTIVITY_THRESHOLD) {
      return true;
    }
  } else {
    // Reset request count if enough time has passed
    session.requestCount = 1;
  }
  
  session.lastRequest = now;
  sessionTracker.set(sessionId, session);
  
  return false;
}

/**
 * Validate session security
 */
export async function validateSessionSecurity(request: NextRequest): Promise<SessionSecurityContext> {
  try {
    const supabase = createClient();
    const deviceFingerprint = generateDeviceFingerprint(request);
    const ipAddress = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || '';
    
    // Get user and session
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return {
        isValid: false,
        userId: null,
        sessionId: null,
        deviceFingerprint,
        ipAddress,
        userAgent,
        securityFlags: {
          suspiciousActivity: false,
          multipleDevices: false,
          locationChange: false,
          rateLimitExceeded: false
        },
        lastActivity: null,
        expiresAt: null
      };
    }
    
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return {
        isValid: false,
        userId: user.id,
        sessionId: null,
        deviceFingerprint,
        ipAddress,
        userAgent,
        securityFlags: {
          suspiciousActivity: false,
          multipleDevices: false,
          locationChange: false,
          rateLimitExceeded: false
        },
        lastActivity: null,
        expiresAt: null
      };
    }
    
    const sessionId = session.access_token.substring(0, 20);
    
    // Check rate limiting
    const rateLimitKey = `${user.id}-${ipAddress}`;
    const rateLimitExceeded = !checkRateLimit(rateLimitKey);
    
    // Check for suspicious activity
    const suspiciousActivity = checkSuspiciousActivity(sessionId, deviceFingerprint);
    
    // Track session
    const now = Date.now();
    const existingSession = sessionTracker.get(sessionId);
    
    if (!existingSession) {
      sessionTracker.set(sessionId, {
        userId: user.id,
        deviceFingerprint,
        ipAddress,
        requestCount: 1,
        lastRequest: now,
        createdAt: now,
        lastActivity: now
      });
    } else {
      existingSession.lastActivity = now;
      sessionTracker.set(sessionId, existingSession);
    }
    
    // Check for multiple devices (simplified)
    const userSessions = Array.from(sessionTracker.values()).filter(s => s.userId === user.id);
    const uniqueDevices = new Set(userSessions.map(s => s.deviceFingerprint));
    const multipleDevices = uniqueDevices.size > 1;
    
    // Location change detection (simplified - would need IP geolocation in production)
    const locationChange = false;
    
    // Check session expiration
    const expiresAt = session.expires_at ? new Date(session.expires_at * 1000) : null;
    const isExpired = expiresAt ? expiresAt.getTime() < now : false;
    
    // Check inactivity timeout
    const lastActivity = existingSession ? new Date(existingSession.lastActivity) : new Date();
    const isInactive = (now - lastActivity.getTime()) > SESSION_SECURITY_CONFIG.INACTIVITY_TIMEOUT;
    
    const isValid = !isExpired && !isInactive && !rateLimitExceeded;
    
    return {
      isValid,
      userId: user.id,
      sessionId,
      deviceFingerprint,
      ipAddress,
      userAgent,
      securityFlags: {
        suspiciousActivity,
        multipleDevices,
        locationChange,
        rateLimitExceeded
      },
      lastActivity,
      expiresAt
    };
    
  } catch (error) {
    console.error('Error validating session security:', error);
    
    return {
      isValid: false,
      userId: null,
      sessionId: null,
      deviceFingerprint: generateDeviceFingerprint(request),
      ipAddress: getClientIP(request),
      userAgent: request.headers.get('user-agent') || '',
      securityFlags: {
        suspiciousActivity: false,
        multipleDevices: false,
        locationChange: false,
        rateLimitExceeded: false
      },
      lastActivity: null,
      expiresAt: null
    };
  }
}

/**
 * Session security middleware
 */
export async function sessionSecurityMiddleware(
  request: NextRequest,
  response: NextResponse
): Promise<NextResponse> {
  const sessionContext = await validateSessionSecurity(request);
  
  // Add security headers
  response.headers.set('X-Session-Valid', sessionContext.isValid.toString());
  response.headers.set('X-Device-Fingerprint', sessionContext.deviceFingerprint);
  
  // Handle security violations
  if (sessionContext.securityFlags.rateLimitExceeded) {
    return new NextResponse('Rate limit exceeded', { status: 429 });
  }
  
  if (sessionContext.securityFlags.suspiciousActivity) {
    // Log suspicious activity
    console.warn('Suspicious activity detected:', {
      userId: sessionContext.userId,
      sessionId: sessionContext.sessionId,
      ipAddress: sessionContext.ipAddress,
      userAgent: sessionContext.userAgent
    });
    
    // Could implement additional security measures here
  }
  
  // Add session context to response headers for client-side access
  if (sessionContext.isValid) {
    response.headers.set('X-Session-Expires', sessionContext.expiresAt?.toISOString() || '');
    response.headers.set('X-Session-Warnings', JSON.stringify({
      suspiciousActivity: sessionContext.securityFlags.suspiciousActivity,
      multipleDevices: sessionContext.securityFlags.multipleDevices,
      locationChange: sessionContext.securityFlags.locationChange
    }));
  }
  
  return response;
}

/**
 * Clean up expired sessions
 */
export function cleanupExpiredSessions(): void {
  const now = Date.now();
  const cutoff = now - SESSION_SECURITY_CONFIG.MAX_SESSION_DURATION;
  
  for (const [sessionId, session] of sessionTracker.entries()) {
    if (session.createdAt < cutoff || session.lastActivity < cutoff) {
      sessionTracker.delete(sessionId);
    }
  }
  
  // Clean up rate limit tracker
  for (const [key, data] of rateLimitTracker.entries()) {
    if (data.resetTime < now) {
      rateLimitTracker.delete(key);
    }
  }
}

// Set up periodic cleanup
setInterval(cleanupExpiredSessions, 5 * 60 * 1000); // Every 5 minutes
